<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Property;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;

class BookingController extends Controller
{
    /**
     * Display a listing of the user's bookings.
     */
    public function index()
    {
        $user = Auth::user();
        $bookings = $user->bookings()
            ->with(['property.location', 'property.images'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('bookings.index', compact('bookings'));
    }

    /**
     * Show the form for creating a new booking.
     */
    public function create(Property $property)
    {
        return view('bookings.create', compact('property'));
    }

    /**
     * Store a newly created booking in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'property_id' => 'required|exists:properties,id',
                'check_in_date' => 'required|date|after_or_equal:today',
                'check_out_date' => 'required|date|after:check_in_date',
                'guests' => 'required|integer|min:1|max:20',
                'special_requests' => 'nullable|string|max:1000',
            ]
        );
        if ($validator->fails()) {
            return back()->with("error", $validator->errors()->first());
        }
        $property = Property::findOrFail($request->property_id);
        $checkIn = Carbon::parse($request->check_in_date);
        $checkOut = Carbon::parse($request->check_out_date);
        $nights = $checkIn->diffInDays($checkOut);

        // Check if property is available for the selected dates
        $isAvailable = $property->isAvailable($checkIn, $checkOut);
        if (!$isAvailable) {
            return back()->withErrors(['dates' => 'Property is not available for the selected dates.']);
        }

        // Check if user has enough credits
        $user = Auth::user();
        if ($user->credits < $nights) {
            return back()->withErrors(['credits' => "You need {$nights} credits for this booking. You currently have {$user->credits} credits."]);
        }

        DB::beginTransaction();
        try {
            // Create the booking
            $booking = Booking::create([
                'user_id' => $user->id,
                'property_id' => $property->id,
                'check_in_date' => $checkIn,
                'check_out_date' => $checkOut,
                'adults' => $request->guests,
                'nights' => $nights,
                'total_price' => $property->price_per_night * $nights,
                'status' => 'confirmed',
                'special_requests' => $request->special_requests,
                'booking_reference' => 'OH-' . strtoupper(uniqid()),
            ]);

            // Deduct credits from user
            $user->decrement('credits', $nights);

            // Create credit transaction record
            $user->creditTransactions()->create([
                'amount' => -$nights,
                'type' => 'debit',
                'description' => "Booking for {$property->name} ({$nights} nights)",
                'booking_id' => $booking->id,
            ]);

            DB::commit();

            // Send confirmation email (will implement later)
            // Mail::to($user->email)->send(new BookingConfirmation($booking));

            return redirect()->route('bookings.show', $booking)
                ->with('success', 'Booking confirmed successfully!');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => $e->getMessage()]);
            
            return back()->withErrors(['error' => 'An error occurred while processing your booking. Please try again.']);
        }
    }

    /**
     * Display the specified booking.
     */
    public function show(Booking $booking)
    {
        // Ensure user can only view their own bookings
        if ($booking->user_id !== Auth::id()) {
            abort(403);
        }

        $booking->load(['property.location', 'property.images', 'user']);

        return view('bookings.show', compact('booking'));
    }

    /**
     * Show the form for editing the specified booking.
     */
    public function edit(Booking $booking)
    {
        // Ensure user can only edit their own bookings
        if ($booking->user_id !== Auth::id()) {
            abort(403);
        }

        // Only allow editing if booking is not in the past and status allows it
        if ($booking->check_in_date->isPast() || !in_array($booking->status, ['confirmed', 'pending'])) {
            return back()->withErrors(['error' => 'This booking cannot be modified.']);
        }

        return view('bookings.edit', compact('booking'));
    }

    /**
     * Update the specified booking in storage.
     */
    public function update(Request $request, Booking $booking)
    {
        // Ensure user can only update their own bookings
        if ($booking->user_id !== Auth::id()) {
            abort(403);
        }

        // Only allow updating if booking is not in the past
        if ($booking->check_in_date->isPast()) {
            return back()->withErrors(['error' => 'Past bookings cannot be modified.']);
        }

        $request->validate([
            'check_in_date' => 'required|date|after_or_equal:today',
            'check_out_date' => 'required|date|after:check_in_date',
            'guests' => 'required|integer|min:1|max:20',
            'special_requests' => 'nullable|string|max:1000',
        ]);

        $checkIn = Carbon::parse($request->check_in_date);
        $checkOut = Carbon::parse($request->check_out_date);
        $newNights = $checkIn->diffInDays($checkOut);
        $oldNights = $booking->nights;
        $creditDifference = $newNights - $oldNights;

        // Check if property is available for the new dates (excluding current booking)
        $isAvailable = $booking->property->isAvailable($checkIn, $checkOut, $booking->id);
        if (!$isAvailable) {
            return back()->withErrors(['dates' => 'Property is not available for the selected dates.']);
        }

        // Check if user has enough credits for the difference
        $user = Auth::user();
        if ($creditDifference > 0 && $user->credits < $creditDifference) {
            return back()->withErrors(['credits' => "You need {$creditDifference} additional credits for this change. You currently have {$user->credits} credits."]);
        }

        DB::beginTransaction();
        try {
            // Update the booking
            $booking->update([
                'check_in_date' => $checkIn,
                'check_out_date' => $checkOut,
                'guests' => $request->guests,
                'nights' => $newNights,
                'total_price' => $booking->property->price_per_night * $newNights,
                'special_requests' => $request->special_requests,
            ]);

            // Adjust user credits if needed
            if ($creditDifference != 0) {
                if ($creditDifference > 0) {
                    $user->decrement('credits', $creditDifference);
                    $description = "Additional credits for booking modification ({$creditDifference} nights)";
                } else {
                    $user->increment('credits', abs($creditDifference));
                    $description = "Credit refund for booking modification (" . abs($creditDifference) . " nights)";
                }

                // Create credit transaction record
                $user->creditTransactions()->create([
                    'amount' => -$creditDifference,
                    'type' => $creditDifference > 0 ? 'debit' : 'credit',
                    'description' => $description,
                    'booking_id' => $booking->id,
                ]);
            }

            DB::commit();

            return redirect()->route('bookings.show', $booking)
                ->with('success', 'Booking updated successfully!');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'An error occurred while updating your booking. Please try again.']);
        }
    }

    /**
     * Remove the specified booking from storage.
     */
    public function destroy(Booking $booking)
    {
        // Ensure user can only cancel their own bookings
        if ($booking->user_id !== Auth::id()) {
            abort(403);
        }

        // Only allow cancellation if booking is not in the past
        if ($booking->check_in_date->isPast()) {
            return back()->withErrors(['error' => 'Past bookings cannot be cancelled.']);
        }

        DB::beginTransaction();
        try {
            // Refund credits to user
            $user = Auth::user();
            $user->increment('credits', $booking->nights);

            // Create credit transaction record
            $user->creditTransactions()->create([
                'amount' => $booking->nights,
                'type' => 'credit',
                'description' => "Refund for cancelled booking: {$booking->property->name}",
                'booking_id' => $booking->id,
            ]);

            // Update booking status instead of deleting
            $booking->update(['status' => 'cancelled']);

            DB::commit();

            return redirect()->route('bookings.index')
                ->with('success', 'Booking cancelled successfully. Credits have been refunded.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'An error occurred while cancelling your booking. Please try again.']);
        }
    }
}
