<?php

namespace App\Http\Controllers;

use App\Models\Location;
use App\Models\Property;
use App\Models\Review;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Show the homepage
     */
    public function index()
    {
        // Get featured properties
        $featuredProperties = Property::with(['location', 'images', 'reviews'])
            ->active()
            ->featured()
            ->take(6)
            ->get();

        // Get popular locations (locations with most properties)
        $popularLocations = Location::withCount('properties')
            ->where('properties_count', '>', 0)
            ->orderBy('properties_count', 'desc')
            ->take(8)
            ->get();


        // Get recent reviews
        $recentReviews = Review::with(['user', 'property'])
            ->approved()
            ->latest()
            ->take(6)
            ->get();

        // Get all locations for search autocomplete
        $allLocations = Location::orderBy('name')->get();

        return view('homepage', compact(
            'featuredProperties',
            'popularLocations',
            'recentReviews',
            'allLocations'
        ));
    }

    /**
     * Search properties
     */
    public function search(Request $request)
    {
        $query = Property::with(['location', 'images', 'reviews'])
            ->active();

        // Location search
        if ($request->filled('location')) {
            $location = $request->location;
            $query->whereHas('location', function ($q) use ($location) {
                $q->where('name', 'like', "%{$location}%")
                    ->orWhere('city', 'like', "%{$location}%")
                    ->orWhere('state', 'like', "%{$location}%")
                    ->orWhere('country', 'like', "%{$location}%");
            });
        }

        // Date availability check
        if ($request->filled('check_in') && $request->filled('check_out')) {
            $checkIn = $request->check_in;
            $checkOut = $request->check_out;

            $query->whereDoesntHave('bookings', function ($q) use ($checkIn, $checkOut) {
                $q->where('status', '!=', 'cancelled')
                    ->where(function ($dateQuery) use ($checkIn, $checkOut) {
                        $dateQuery->whereBetween('check_in_date', [$checkIn, $checkOut])
                            ->orWhereBetween('check_out_date', [$checkIn, $checkOut])
                            ->orWhere(function ($overlapQuery) use ($checkIn, $checkOut) {
                                $overlapQuery->where('check_in_date', '<=', $checkIn)
                                    ->where('check_out_date', '>=', $checkOut);
                            });
                    });
            })
                ->whereDoesntHave('unavailableDates', function ($q) use ($checkIn, $checkOut) {
                    $q->whereBetween('date', [$checkIn, $checkOut]);
                });
        }

        // Guest capacity
        if ($request->filled('guests')) {
            $query->where('guest_capacity', '>=', $request->guests);
        }

        // Price range
        if ($request->filled('min_price')) {
            $query->where('price_per_night', '>=', $request->min_price);
        }
        if ($request->filled('max_price')) {
            $query->where('price_per_night', '<=', $request->max_price);
        }

        // Sorting
        $sortBy = $request->get('sort', 'featured');
        switch ($sortBy) {
            case 'price_low':
                $query->orderBy('price_per_night', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price_per_night', 'desc');
                break;
            case 'rating':
                $query->withAvg('reviews', 'rating')
                    ->orderBy('reviews_avg_rating', 'desc');
                break;
            case 'newest':
                $query->latest();
                break;
            default:
                $query->orderBy('featured', 'desc')
                    ->latest();
        }

        $properties = $query->paginate(12);
        $locations = Location::orderBy('name')->get();

        return view('properties.search', compact('properties', 'locations'));
    }

    /**
     * Get location suggestions for autocomplete
     */
    public function locationSuggestions(Request $request)
    {
        $term = $request->get('term', '');

        $locations = Location::where(function ($query) use ($term) {
            $query->where('name', 'like', "%{$term}%")
                ->orWhere('city', 'like', "%{$term}%")
                ->orWhere('state', 'like', "%{$term}%")
                ->orWhere('country', 'like', "%{$term}%");
        })
            ->limit(10)
            ->get()
            ->map(function ($location) {
                return [
                    'id' => $location->id,
                    'label' => $location->full_name,
                    'value' => $location->name
                ];
            });

        return response()->json($locations);
    }
}
