<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CreditController extends Controller
{
    /**
     * Display the user's credit history and balance
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // Get credit transactions with pagination
        $transactions = $user->creditTransactions()
            ->with('booking.property')
            ->orderBy('created_at', 'desc')
            ->paginate(15);
        
        // Filter by type if requested
        if ($request->has('type') && in_array($request->type, ['credit', 'debit'])) {
            $transactions = $user->creditTransactions()
                ->with('booking.property')
                ->where('type', $request->type)
                ->orderBy('created_at', 'desc')
                ->paginate(15);
        }
        
        // Calculate statistics
        $totalCreditsEarned = $user->creditTransactions()->credits()->sum('amount');
        $totalCreditsUsed = abs($user->creditTransactions()->debits()->sum('amount'));
        $totalTransactions = $user->creditTransactions()->count();
        
        return view('credits.index', compact(
            'user',
            'transactions',
            'totalCreditsEarned',
            'totalCreditsUsed',
            'totalTransactions'
        ));
    }
}
