<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\CreditTransaction;
use App\Models\Property;
use App\Models\Review;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AdminController extends Controller
{
    /**
     * Show admin login form
     */
    public function showLogin()
    {
        if (Auth::check() && Auth::user()->isAdmin()) {
            return redirect()->route('admin.dashboard');
        }

        return view('admin.auth.login');
    }

    /**
     * Handle admin login
     */
    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required'],
        ]);

        if (Auth::attempt($credentials, $request->boolean('remember'))) {
            if (Auth::user()->isAdmin()) {
                $request->session()->regenerate();
                return redirect()->intended(route('admin.dashboard'));
            } else {
                Auth::logout();
                return back()->withErrors([
                    'email' => 'Access denied. Admin privileges required.',
                ]);
            }
        }

        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ]);
    }

    /**
     * Handle admin logout
     */
    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('admin.login');
    }

    /**
     * Show admin dashboard
     */
    public function dashboard()
    {
        $stats = [
            'total_properties' => Property::count(),
            'active_properties' => Property::where('status', 'active')->count(),
            'total_bookings' => Booking::count(),
            'pending_bookings' => Booking::where('status', 'pending')->count(),
            'confirmed_bookings' => Booking::where('status', 'confirmed')->count(),
            'total_users' => User::where('role', 'user')->count(),
            'total_reviews' => Review::count(),
            'pending_reviews' => Review::where('status', 'pending')->count(),
            'total_credits_issued' => CreditTransaction::where('type', 'credit')->sum('amount'),
            'total_credits_used' => CreditTransaction::where('type', 'debit')->sum('amount'),
        ];

        $recent_bookings = Booking::with(['user', 'property'])
            ->latest()
            ->take(5)
            ->get();

        $recent_reviews = Review::with(['user', 'property'])
            ->where('status', 'pending')
            ->latest()
            ->take(5)
            ->get();

        // Chart data for analytics
        $chartData = $this->getChartData();

        return view('admin.dashboard', compact('stats', 'recent_bookings', 'recent_reviews', 'chartData'));
    }

    private function getChartData()
    {
        // Booking trends for last 30 days
        $bookingTrends = $this->getBookingTrends();

        // Credit usage for last 7 days
        $creditUsage = $this->getCreditUsage();

        // Popular locations
        $popularLocations = $this->getPopularLocations();

        return [
            'booking_trends' => $bookingTrends,
            'credit_usage' => $creditUsage,
            'popular_locations' => $popularLocations,
        ];
    }

    private function getBookingTrends()
    {
        $days = [];
        $data = [];

        for ($i = 29; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $days[] = $date->format('M j');

            $bookingCount = Booking::whereDate('created_at', $date->toDateString())->count();
            $data[] = $bookingCount;
        }

        return [
            'labels' => $days,
            'data' => $data,
        ];
    }

    private function getCreditUsage()
    {
        $days = [];
        $data = [];

        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $days[] = $date->format('M j');

            $creditsUsed = CreditTransaction::where('type', 'debit')
                ->whereDate('created_at', $date->toDateString())
                ->sum('amount');
            $data[] = abs($creditsUsed); // Make positive for display
        }

        return [
            'labels' => $days,
            'data' => $data,
        ];
    }

    private function getPopularLocations()
    {
        $locations = Property::select('location', DB::raw('count(*) as booking_count'))
            ->join('bookings', 'properties.id', '=', 'bookings.property_id')
            ->groupBy('location')
            ->orderBy('booking_count', 'desc')
            ->take(5)
            ->get();

        return [
            'labels' => $locations->pluck('location')->toArray(),
            'data' => $locations->pluck('booking_count')->toArray(),
        ];
    }
}
