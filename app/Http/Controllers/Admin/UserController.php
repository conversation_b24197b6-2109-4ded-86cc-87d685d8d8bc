<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CreditTransaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    /**
     * Display a listing of users
     */
    public function index(Request $request)
    {
        $query = User::where('role', 'user')->with(['bookings', 'creditTransactions']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Filter by credit range
        if ($request->filled('min_credits')) {
            $query->where('credits', '>=', $request->min_credits);
        }
        if ($request->filled('max_credits')) {
            $query->where('credits', '<=', $request->max_credits);
        }

        // Filter by registration date
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Sort options
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $users = $query->paginate(15)->withQueryString();

        return view('admin.users.index', compact('users'));
    }

    /**
     * Show the form for creating a new user
     */
    public function create()
    {
        return view('admin.users.create');
    }

    /**
     * Store a newly created user
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'phone' => ['nullable', 'string', 'max:20'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'credits' => ['required', 'integer', 'min:0'],
            'avatar' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
        ]);

        DB::beginTransaction();
        try {
            $userData = [
                'name' => $validated['name'],
                'email' => $validated['email'],
                'phone' => $validated['phone'],
                'password' => Hash::make($validated['password']),
                'credits' => $validated['credits'],
                'role' => 'user',
                'email_verified_at' => now(),
            ];

            // Handle avatar upload
            if ($request->hasFile('avatar')) {
                $userData['avatar'] = $request->file('avatar')->store('avatars', 'public');
            }

            $user = User::create($userData);

            // Create initial credit transaction if credits > 0
            if ($validated['credits'] > 0) {
                CreditTransaction::create([
                    'user_id' => $user->id,
                    'type' => 'credit',
                    'amount' => $validated['credits'],
                    'description' => 'Initial credits assigned by admin',
                    'reference_type' => 'admin_assignment',
                    'reference_id' => auth()->id(),
                ]);
            }

            DB::commit();
            return redirect()->route('admin.users.index')
                ->with('success', 'User created successfully!');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                ->with('error', 'Failed to create user. Please try again.');
        }
    }

    /**
     * Display the specified user
     */
    public function show(User $user)
    {
        $user->load(['bookings.property', 'creditTransactions', 'reviews.property']);
        
        $stats = [
            'total_bookings' => $user->bookings->count(),
            'confirmed_bookings' => $user->bookings->where('status', 'confirmed')->count(),
            'total_spent' => $user->creditTransactions->where('type', 'debit')->sum('amount'),
            'total_earned' => $user->creditTransactions->where('type', 'credit')->sum('amount'),
            'total_reviews' => $user->reviews->count(),
            'avg_rating' => $user->reviews->avg('rating'),
        ];

        return view('admin.users.show', compact('user', 'stats'));
    }

    /**
     * Show the form for editing the specified user
     */
    public function edit(User $user)
    {
        return view('admin.users.edit', compact('user'));
    }

    /**
     * Update the specified user
     */
    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => ['nullable', 'string', 'max:20'],
            'password' => ['nullable', 'string', 'min:8', 'confirmed'],
            'avatar' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
        ]);

        DB::beginTransaction();
        try {
            $userData = [
                'name' => $validated['name'],
                'email' => $validated['email'],
                'phone' => $validated['phone'],
            ];

            // Update password if provided
            if (!empty($validated['password'])) {
                $userData['password'] = Hash::make($validated['password']);
            }

            // Handle avatar upload
            if ($request->hasFile('avatar')) {
                // Delete old avatar
                if ($user->avatar) {
                    Storage::disk('public')->delete($user->avatar);
                }
                $userData['avatar'] = $request->file('avatar')->store('avatars', 'public');
            }

            $user->update($userData);

            DB::commit();
            return redirect()->route('admin.users.show', $user)
                ->with('success', 'User updated successfully!');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                ->with('error', 'Failed to update user. Please try again.');
        }
    }

    /**
     * Remove the specified user
     */
    public function destroy(User $user)
    {
        if ($user->bookings()->exists()) {
            return back()->with('error', 'Cannot delete user with existing bookings.');
        }

        DB::beginTransaction();
        try {
            // Delete avatar
            if ($user->avatar) {
                Storage::disk('public')->delete($user->avatar);
            }

            // Delete credit transactions
            $user->creditTransactions()->delete();
            
            // Delete reviews
            $user->reviews()->delete();

            // Delete user
            $user->delete();

            DB::commit();
            return redirect()->route('admin.users.index')
                ->with('success', 'User deleted successfully!');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Failed to delete user. Please try again.');
        }
    }

    /**
     * Manage user credits
     */
    public function manageCredits(Request $request, User $user)
    {
        $validated = $request->validate([
            'action' => ['required', 'in:add,deduct'],
            'amount' => ['required', 'integer', 'min:1'],
            'description' => ['required', 'string', 'max:255'],
        ]);

        DB::beginTransaction();
        try {
            $amount = $validated['amount'];
            $type = $validated['action'] === 'add' ? 'credit' : 'debit';
            
            if ($type === 'debit') {
                if ($user->credits < $amount) {
                    return back()->with('error', 'User does not have enough credits.');
                }
                $amount = -$amount; // Make negative for debit
            }

            // Update user credits
            $user->increment('credits', $amount);

            // Create transaction record
            CreditTransaction::create([
                'user_id' => $user->id,
                'type' => $type,
                'amount' => abs($amount),
                'description' => $validated['description'],
                'reference_type' => 'admin_adjustment',
                'reference_id' => auth()->id(),
            ]);

            DB::commit();
            return back()->with('success', 'Credits updated successfully!');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Failed to update credits. Please try again.');
        }
    }

    /**
     * Export users data
     */
    public function export(Request $request)
    {
        $users = User::where('role', 'user')
            ->with(['bookings', 'creditTransactions'])
            ->get();

        $filename = 'users_export_' . now()->format('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($users) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'ID', 'Name', 'Email', 'Phone', 'Credits', 'Total Bookings', 
                'Total Spent', 'Registration Date', 'Last Login'
            ]);

            foreach ($users as $user) {
                fputcsv($file, [
                    $user->id,
                    $user->name,
                    $user->email,
                    $user->phone,
                    $user->credits,
                    $user->bookings->count(),
                    $user->creditTransactions->where('type', 'debit')->sum('amount'),
                    $user->created_at->format('Y-m-d H:i:s'),
                    $user->updated_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
