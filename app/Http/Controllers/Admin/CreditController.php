<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CreditTransaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CreditController extends Controller
{
    /**
     * Display credit system dashboard
     */
    public function index(Request $request)
    {
        // Get credit statistics
        $stats = [
            'total_credits_issued' => CreditTransaction::where('type', 'credit')->sum('amount'),
            'total_credits_used' => CreditTransaction::where('type', 'debit')->sum('amount'),
            'active_users_with_credits' => User::where('credits', '>', 0)->count(),
            'total_transactions' => CreditTransaction::count(),
            'credits_in_circulation' => User::sum('credits'),
            'average_credits_per_user' => User::avg('credits'),
        ];

        // Get recent transactions
        $recentTransactions = CreditTransaction::with('user')
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        // Get top users by credits
        $topUsers = User::orderBy('credits', 'desc')
            ->take(10)
            ->get();

        // Get monthly credit trends
        $monthlyTrends = CreditTransaction::select(
                DB::raw('MONTH(created_at) as month'),
                DB::raw('YEAR(created_at) as year'),
                DB::raw('SUM(CASE WHEN type = "credit" THEN amount ELSE 0 END) as credits_issued'),
                DB::raw('SUM(CASE WHEN type = "debit" THEN amount ELSE 0 END) as credits_used')
            )
            ->whereYear('created_at', now()->year)
            ->groupBy('year', 'month')
            ->orderBy('month')
            ->get();

        return view('admin.credits.index', compact('stats', 'recentTransactions', 'topUsers', 'monthlyTrends'));
    }

    /**
     * Display credit transactions
     */
    public function transactions(Request $request)
    {
        $query = CreditTransaction::with('user');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('description', 'like', "%{$search}%")
                  ->orWhere('reference_type', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by reference type
        if ($request->filled('reference_type')) {
            $query->where('reference_type', $request->reference_type);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Filter by amount range
        if ($request->filled('amount_min')) {
            $query->where('amount', '>=', $request->amount_min);
        }
        if ($request->filled('amount_max')) {
            $query->where('amount', '<=', $request->amount_max);
        }

        // Sort options
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $transactions = $query->paginate(20)->withQueryString();

        // Get reference types for filter
        $referenceTypes = CreditTransaction::select('reference_type')
            ->distinct()
            ->whereNotNull('reference_type')
            ->pluck('reference_type')
            ->sort();

        return view('admin.credits.transactions', compact('transactions', 'referenceTypes'));
    }

    /**
     * Bulk credit operations
     */
    public function bulkOperations()
    {
        $users = User::select('id', 'name', 'email', 'credits')
            ->orderBy('name')
            ->get();

        return view('admin.credits.bulk-operations', compact('users'));
    }

    /**
     * Process bulk credit operations
     */
    public function processBulkOperations(Request $request)
    {
        $validated = $request->validate([
            'operation' => ['required', 'in:add,deduct,set'],
            'amount' => ['required', 'integer', 'min:0'],
            'description' => ['required', 'string', 'max:255'],
            'user_ids' => ['required', 'array', 'min:1'],
            'user_ids.*' => ['exists:users,id'],
        ]);

        $operation = $validated['operation'];
        $amount = $validated['amount'];
        $description = $validated['description'];
        $userIds = $validated['user_ids'];

        DB::beginTransaction();
        try {
            $users = User::whereIn('id', $userIds)->get();
            $processedCount = 0;

            foreach ($users as $user) {
                $oldCredits = $user->credits;
                $newCredits = $oldCredits;

                switch ($operation) {
                    case 'add':
                        $newCredits = $oldCredits + $amount;
                        $transactionType = 'credit';
                        $transactionAmount = $amount;
                        break;
                    case 'deduct':
                        if ($oldCredits < $amount) {
                            continue 2; // Skip users who don't have enough credits
                        }
                        $newCredits = $oldCredits - $amount;
                        $transactionType = 'debit';
                        $transactionAmount = $amount;
                        break;
                    case 'set':
                        $newCredits = $amount;
                        $transactionType = $amount > $oldCredits ? 'credit' : 'debit';
                        $transactionAmount = abs($amount - $oldCredits);
                        break;
                }

                if ($newCredits !== $oldCredits) {
                    $user->update(['credits' => $newCredits]);

                    // Create transaction record
                    CreditTransaction::create([
                        'user_id' => $user->id,
                        'type' => $transactionType,
                        'amount' => $transactionAmount,
                        'description' => $description,
                        'reference_type' => 'bulk_operation',
                        'reference_id' => 1, // Admin user ID
                    ]);

                    $processedCount++;
                }
            }

            DB::commit();
            return back()->with('success', "Bulk operation completed successfully! Processed {$processedCount} users.");

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Failed to process bulk operation. Please try again.');
        }
    }

    /**
     * Credit analytics data
     */
    public function analytics()
    {
        // Monthly credit flow
        $monthlyFlow = CreditTransaction::select(
                DB::raw('MONTH(created_at) as month'),
                DB::raw('YEAR(created_at) as year'),
                DB::raw('SUM(CASE WHEN type = "credit" THEN amount ELSE 0 END) as issued'),
                DB::raw('SUM(CASE WHEN type = "debit" THEN amount ELSE 0 END) as used')
            )
            ->whereYear('created_at', now()->year)
            ->groupBy('year', 'month')
            ->orderBy('month')
            ->get();

        // Credit distribution by reference type
        $referenceTypeDistribution = CreditTransaction::select(
                'reference_type',
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(amount) as total_amount')
            )
            ->groupBy('reference_type')
            ->orderBy('total_amount', 'desc')
            ->get();

        // User credit distribution
        $userDistribution = User::select(
                DB::raw('CASE 
                    WHEN credits = 0 THEN "0 Credits"
                    WHEN credits BETWEEN 1 AND 5 THEN "1-5 Credits"
                    WHEN credits BETWEEN 6 AND 10 THEN "6-10 Credits"
                    WHEN credits BETWEEN 11 AND 20 THEN "11-20 Credits"
                    WHEN credits BETWEEN 21 AND 50 THEN "21-50 Credits"
                    ELSE "50+ Credits"
                END as credit_range'),
                DB::raw('COUNT(*) as user_count')
            )
            ->groupBy('credit_range')
            ->get();

        // Daily transaction volume (last 30 days)
        $dailyVolume = CreditTransaction::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as transaction_count'),
                DB::raw('SUM(amount) as total_amount')
            )
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return response()->json([
            'monthly_flow' => $monthlyFlow,
            'reference_type_distribution' => $referenceTypeDistribution,
            'user_distribution' => $userDistribution,
            'daily_volume' => $dailyVolume,
        ]);
    }

    /**
     * Export credit transactions
     */
    public function exportTransactions(Request $request)
    {
        $query = CreditTransaction::with('user');

        // Apply same filters as transactions method
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('description', 'like', "%{$search}%")
                  ->orWhere('reference_type', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('reference_type')) {
            $query->where('reference_type', $request->reference_type);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('amount_min')) {
            $query->where('amount', '>=', $request->amount_min);
        }
        if ($request->filled('amount_max')) {
            $query->where('amount', '<=', $request->amount_max);
        }

        $transactions = $query->orderBy('created_at', 'desc')->get();

        $filename = 'credit_transactions_' . now()->format('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($transactions) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'Transaction ID', 'User Name', 'User Email', 'Type', 'Amount', 
                'Description', 'Reference Type', 'Reference ID', 'Date'
            ]);

            foreach ($transactions as $transaction) {
                fputcsv($file, [
                    $transaction->id,
                    $transaction->user->name,
                    $transaction->user->email,
                    ucfirst($transaction->type),
                    $transaction->amount,
                    $transaction->description,
                    $transaction->reference_type,
                    $transaction->reference_id,
                    $transaction->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Generate credit system reports
     */
    public function reports()
    {
        $reports = [
            'monthly_summary' => $this->getMonthlyReport(),
            'user_activity' => $this->getUserActivityReport(),
            'transaction_summary' => $this->getTransactionSummaryReport(),
        ];

        return view('admin.credits.reports', compact('reports'));
    }

    private function getMonthlyReport()
    {
        return CreditTransaction::select(
                DB::raw('MONTH(created_at) as month'),
                DB::raw('YEAR(created_at) as year'),
                DB::raw('SUM(CASE WHEN type = "credit" THEN amount ELSE 0 END) as credits_issued'),
                DB::raw('SUM(CASE WHEN type = "debit" THEN amount ELSE 0 END) as credits_used'),
                DB::raw('COUNT(CASE WHEN type = "credit" THEN 1 END) as credit_transactions'),
                DB::raw('COUNT(CASE WHEN type = "debit" THEN 1 END) as debit_transactions')
            )
            ->whereYear('created_at', now()->year)
            ->groupBy('year', 'month')
            ->orderBy('month')
            ->get();
    }

    private function getUserActivityReport()
    {
        return User::select(
                'users.id',
                'users.name',
                'users.email',
                'users.credits',
                DB::raw('COUNT(credit_transactions.id) as total_transactions'),
                DB::raw('SUM(CASE WHEN credit_transactions.type = "credit" THEN credit_transactions.amount ELSE 0 END) as total_credits_received'),
                DB::raw('SUM(CASE WHEN credit_transactions.type = "debit" THEN credit_transactions.amount ELSE 0 END) as total_credits_used')
            )
            ->leftJoin('credit_transactions', 'users.id', '=', 'credit_transactions.user_id')
            ->groupBy('users.id', 'users.name', 'users.email', 'users.credits')
            ->having('total_transactions', '>', 0)
            ->orderBy('total_transactions', 'desc')
            ->take(50)
            ->get();
    }

    private function getTransactionSummaryReport()
    {
        return CreditTransaction::select(
                'reference_type',
                DB::raw('COUNT(*) as transaction_count'),
                DB::raw('SUM(amount) as total_amount'),
                DB::raw('AVG(amount) as average_amount'),
                DB::raw('MIN(amount) as min_amount'),
                DB::raw('MAX(amount) as max_amount')
            )
            ->groupBy('reference_type')
            ->orderBy('total_amount', 'desc')
            ->get();
    }
}
