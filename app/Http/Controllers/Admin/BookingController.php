<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\CreditTransaction;
use App\Models\Property;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BookingController extends Controller
{
    /**
     * Display a listing of bookings
     */
    public function index(Request $request)
    {
        $query = Booking::with(['user', 'property']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('booking_reference', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  })
                  ->orWhereHas('property', function ($propertyQuery) use ($search) {
                      $propertyQuery->where('title', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('check_in_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('check_out_date', '<=', $request->date_to);
        }

        // Filter by property
        if ($request->filled('property_id')) {
            $query->where('property_id', $request->property_id);
        }

        // Sort options
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $bookings = $query->paginate(15)->withQueryString();

        // Get properties for filter dropdown
        $properties = Property::select('id', 'name')->orderBy('name')->get();

        // Get statistics
        $stats = [
            'total_bookings' => Booking::count(),
            'pending_bookings' => Booking::where('status', 'pending')->count(),
            'confirmed_bookings' => Booking::where('status', 'confirmed')->count(),
            'cancelled_bookings' => Booking::where('status', 'cancelled')->count(),
            'total_revenue' => Booking::where('status', 'confirmed')->sum('total_price'),
            'this_month_bookings' => Booking::whereMonth('created_at', now()->month)->count(),
        ];

        return view('admin.bookings.index', compact('bookings', 'properties', 'stats'));
    }

    /**
     * Display the specified booking
     */
    public function show(Booking $booking)
    {
        $booking->load(['user', 'property.images']);
        
        return view('admin.bookings.show', compact('booking'));
    }

    /**
     * Show the form for editing the specified booking
     */
    public function edit(Booking $booking)
    {
        $booking->load(['user', 'property']);
        return view('admin.bookings.edit', compact('booking'));
    }

    /**
     * Update the specified booking
     */
    public function update(Request $request, Booking $booking)
    {
        $validated = $request->validate([
            'adults' => ['required', 'integer', 'min:1', 'max:10'],
            'children' => ['required', 'integer', 'min:0', 'max:10'],
            'special_requests' => ['nullable', 'string', 'max:500'],
            'admin_notes' => ['nullable', 'string', 'max:500'],
        ]);

        $booking->update($validated);

        return redirect()->route('admin.bookings.show', $booking)
            ->with('success', 'Booking updated successfully!');
    }

    /**
     * Update booking status
     */
    public function updateStatus(Request $request, Booking $booking)
    {
        $validated = $request->validate([
            'status' => ['required', 'in:pending,confirmed,cancelled'],
            'admin_notes' => ['nullable', 'string', 'max:500'],
        ]);

        $oldStatus = $booking->status;
        $newStatus = $validated['status'];

        DB::beginTransaction();
        try {
            // Handle status change logic
            if ($oldStatus !== $newStatus) {
                if ($newStatus === 'cancelled' && $oldStatus === 'confirmed') {
                    // Refund credits if booking was confirmed and now cancelled
                    $nights = $booking->check_in_date->diffInDays($booking->check_out_date);
                    $booking->user->increment('credits', $nights);

                    // Create refund transaction
                    CreditTransaction::create([
                        'user_id' => $booking->user_id,
                        'type' => 'credit',
                        'amount' => $nights,
                        'description' => "Refund for cancelled booking #{$booking->booking_reference}",
                        'reference_type' => 'booking_cancellation',
                        'reference_id' => $booking->id,
                    ]);
                }

                if ($newStatus === 'confirmed' && $oldStatus === 'cancelled') {
                    // Deduct credits again if booking was cancelled and now confirmed
                    $nights = $booking->check_in_date->diffInDays($booking->check_out_date);
                    
                    if ($booking->user->credits < $nights) {
                        return back()->with('error', 'User does not have enough credits to confirm this booking.');
                    }

                    $booking->user->decrement('credits', $nights);

                    // Create debit transaction
                    CreditTransaction::create([
                        'user_id' => $booking->user_id,
                        'type' => 'debit',
                        'amount' => $nights,
                        'description' => "Payment for confirmed booking #{$booking->booking_reference}",
                        'reference_type' => 'booking_confirmation',
                        'reference_id' => $booking->id,
                    ]);
                }
            }

            // Update booking
            $booking->update([
                'status' => $newStatus,
                'admin_notes' => $validated['admin_notes'],
            ]);

            DB::commit();
            return back()->with('success', 'Booking status updated successfully!');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Failed to update booking status. Please try again.');
        }
    }

    /**
     * Export bookings data
     */
    public function export(Request $request)
    {
        $query = Booking::with(['user', 'property']);

        // Apply same filters as index
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('booking_reference', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  })
                  ->orWhereHas('property', function ($propertyQuery) use ($search) {
                      $propertyQuery->where('title', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('check_in_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('check_out_date', '<=', $request->date_to);
        }

        if ($request->filled('property_id')) {
            $query->where('property_id', $request->property_id);
        }

        $bookings = $query->orderBy('created_at', 'desc')->get();

        $filename = 'bookings_export_' . now()->format('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($bookings) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'Booking Reference', 'User Name', 'User Email', 'Property', 
                'Check In', 'Check Out', 'Nights', 'Adults', 'Children', 
                'Status', 'Total Price', 'Booking Date', 'Special Requests'
            ]);

            foreach ($bookings as $booking) {
                fputcsv($file, [
                    $booking->booking_reference,
                    $booking->user->name,
                    $booking->user->email,
                    $booking->property->title,
                    $booking->check_in_date->format('Y-m-d'),
                    $booking->check_out_date->format('Y-m-d'),
                    $booking->check_in_date->diffInDays($booking->check_out_date),
                    $booking->adults,
                    $booking->children,
                    ucfirst($booking->status),
                    $booking->total_price,
                    $booking->created_at->format('Y-m-d H:i:s'),
                    $booking->special_requests,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get booking analytics data
     */
    public function analytics()
    {
        $monthlyBookings = Booking::select(
                DB::raw('MONTH(created_at) as month'),
                DB::raw('YEAR(created_at) as year'),
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(total_price) as revenue')
            )
            ->whereYear('created_at', now()->year)
            ->groupBy('year', 'month')
            ->orderBy('month')
            ->get();

        $statusDistribution = Booking::select('status', DB::raw('COUNT(*) as count'))
            ->groupBy('status')
            ->get();

        $topProperties = Property::select('properties.title', DB::raw('COUNT(bookings.id) as booking_count'))
            ->join('bookings', 'properties.id', '=', 'bookings.property_id')
            ->groupBy('properties.id', 'properties.title')
            ->orderBy('booking_count', 'desc')
            ->take(10)
            ->get();

        return response()->json([
            'monthly_bookings' => $monthlyBookings,
            'status_distribution' => $statusDistribution,
            'top_properties' => $topProperties,
        ]);
    }

    /**
     * Bulk update booking statuses
     */
    public function bulkUpdate(Request $request)
    {
        $validated = $request->validate([
            'booking_ids' => ['required', 'array'],
            'booking_ids.*' => ['exists:bookings,id'],
            'action' => ['required', 'in:confirm,cancel,pending'],
        ]);

        $bookingIds = $validated['booking_ids'];
        $action = $validated['action'];
        $status = $action === 'confirm' ? 'confirmed' : ($action === 'cancel' ? 'cancelled' : 'pending');

        DB::beginTransaction();
        try {
            $bookings = Booking::whereIn('id', $bookingIds)->with('user')->get();
            
            foreach ($bookings as $booking) {
                $oldStatus = $booking->status;
                
                if ($oldStatus !== $status) {
                    // Handle credit transactions for status changes
                    if ($status === 'cancelled' && $oldStatus === 'confirmed') {
                        $nights = $booking->check_in_date->diffInDays($booking->check_out_date);
                        $booking->user->increment('credits', $nights);

                        CreditTransaction::create([
                            'user_id' => $booking->user_id,
                            'type' => 'credit',
                            'amount' => $nights,
                            'description' => "Refund for cancelled booking #{$booking->booking_reference}",
                            'reference_type' => 'booking_cancellation',
                            'reference_id' => $booking->id,
                        ]);
                    }

                    if ($status === 'confirmed' && $oldStatus === 'cancelled') {
                        $nights = $booking->check_in_date->diffInDays($booking->check_out_date);
                        
                        if ($booking->user->credits < $nights) {
                            continue; // Skip this booking if user doesn't have enough credits
                        }

                        $booking->user->decrement('credits', $nights);

                        CreditTransaction::create([
                            'user_id' => $booking->user_id,
                            'type' => 'debit',
                            'amount' => $nights,
                            'description' => "Payment for confirmed booking #{$booking->booking_reference}",
                            'reference_type' => 'booking_confirmation',
                            'reference_id' => $booking->id,
                        ]);
                    }

                    $booking->update(['status' => $status]);
                }
            }

            DB::commit();
            return back()->with('success', 'Bookings updated successfully!');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Failed to update bookings. Please try again.');
        }
    }
}
