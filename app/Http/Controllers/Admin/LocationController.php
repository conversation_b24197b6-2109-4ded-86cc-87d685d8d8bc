<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Location;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class LocationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Location::withCount('properties');

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Country filter
        if ($request->filled('country')) {
            $query->where('country', $request->country);
        }

        $locations = $query->orderBy('name')->paginate(15)->withQueryString();

        // Get countries for filter
        $countries = Location::distinct()->pluck('country')->filter()->sort();

        // Statistics
        $stats = [
            'total_locations' => Location::count(),
            'total_properties' => Location::withCount('properties')->get()->sum('properties_count'),
            'countries_count' => Location::distinct('country')->count(),
            'cities_count' => Location::distinct('city')->count(),
        ];

        return view('admin.locations.index', compact('locations', 'countries', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.locations.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:locations',
            'country' => 'required|string|max:255',
            'state' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('locations', 'public');
            $validated['image_url'] = $imagePath;
        }

        // Generate slug
        $validated['slug'] = Str::slug($validated['name']);

        Location::create($validated);

        return redirect()->route('admin.locations.index')
            ->with('success', 'Location created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Location $location)
    {
        $location->load(['properties' => function ($query) {
            $query->withCount('reviews')->with('images');
        }]);

        // Analytics data
        $analytics = [
            'total_properties' => $location->properties->count(),
            'active_properties' => $location->properties->where('status', 'active')->count(),
            'total_bookings' => $location->properties->sum(function ($property) {
                return $property->bookings()->count();
            }),
            'average_rating' => $location->properties->avg('average_rating'),
            'total_reviews' => $location->properties->sum('reviews_count'),
        ];

        return view('admin.locations.show', compact('location', 'analytics'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Location $location)
    {
        return view('admin.locations.edit', compact('location'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Location $location)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:locations,name,' . $location->id,
            'country' => 'required|string|max:255',
            'state' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($location->image_url) {
                Storage::disk('public')->delete($location->image_url);
            }

            $imagePath = $request->file('image')->store('locations', 'public');
            $validated['image_url'] = $imagePath;
        }

        // Update slug if name changed
        if ($validated['name'] !== $location->name) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        $location->update($validated);

        return redirect()->route('admin.locations.index')
            ->with('success', 'Location updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Location $location)
    {
        // Check if location has properties
        if ($location->properties()->count() > 0) {
            return redirect()->route('admin.locations.index')
                ->with('error', 'Cannot delete location with existing properties.');
        }

        // Delete image
        if ($location->image_url) {
            Storage::disk('public')->delete($location->image_url);
        }

        $location->delete();

        return redirect()->route('admin.locations.index')
            ->with('success', 'Location deleted successfully.');
    }

    /**
     * Get analytics data for locations
     */
    public function analytics()
    {
        $locations = Location::withCount(['properties' => function ($query) {
            $query->where('status', 'active');
        }])->get();

        $analytics = [
            'locations_by_country' => Location::selectRaw('country, COUNT(*) as count')
                ->groupBy('country')
                ->orderBy('count', 'desc')
                ->get(),
            'properties_by_location' => $locations->map(function ($location) {
                return [
                    'name' => $location->name,
                    'properties_count' => $location->properties_count,
                ];
            })->sortByDesc('properties_count')->take(10),
        ];

        return response()->json($analytics);
    }
}
