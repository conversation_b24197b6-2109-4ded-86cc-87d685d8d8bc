<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;

class ThemeServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Share theme configuration with all views
        View::share('theme', config('theme'));

        // Create helper functions for theme colors
        if (!function_exists('theme_color')) {
            function theme_color($key = null, $default = null) {
                if ($key === null) {
                    return config('theme.colors');
                }

                return config("theme.colors.{$key}", $default);
            }
        }

        if (!function_exists('primary_color')) {
            function primary_color() {
                return config('theme.primary_color');
            }
        }

        if (!function_exists('secondary_color')) {
            function secondary_color() {
                return config('theme.secondary_color');
            }
        }
    }
}
