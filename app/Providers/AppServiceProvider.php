<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register custom Blade directives for theme
        Blade::directive('themeColor', function ($expression) {
            return "<?php echo config('theme.colors.' . {$expression}); ?>";
        });

        Blade::directive('primaryColor', function () {
            return "<?php echo config('theme.primary_color'); ?>";
        });

        Blade::directive('secondaryColor', function () {
            return "<?php echo config('theme.secondary_color'); ?>";
        });
    }
}
