<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PropertyImage extends Model
{
    use HasFactory;

    protected $fillable = [
        'property_id',
        'image_path',
        'alt_text',
        'sort_order',
        'is_primary',
    ];

    protected $casts = [
        'is_primary' => 'boolean',
    ];

    /**
     * Get the property that owns the image
     */
    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class);
    }

    /**
     * Get the full URL of the image
     */
    public function getUrlAttribute(): string
    {
        // For external URLs (like Unsplash), return as-is
        if (filter_var($this->image_path, FILTER_VALIDATE_URL)) {
            return $this->image_path;
        }

        // For local files, prepend storage path
        return asset('storage/' . $this->image_path);
    }

    /**
     * Get the image URL (alias for compatibility)
     */
    public function getImageUrlAttribute(): string
    {
        // For external URLs (like Unsplash), return as-is
        if (filter_var($this->image_path, FILTER_VALIDATE_URL)) {
            return $this->image_path;
        }

        // For local files, prepend storage path
        return asset('storage/' . $this->image_path);
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        // When setting an image as primary, unset all other primary images for the same property
        static::saving(function ($image) {
            if ($image->is_primary) {
                static::where('property_id', $image->property_id)
                    ->where('id', '!=', $image->id)
                    ->update(['is_primary' => false]);
            }
        });
    }
}
