<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Oasis Homestay Theme Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the theme configuration for the Oasis Homestay
    | application. These colors are used consistently across all components
    | including frontend, admin panel, buttons, forms, and navigation.
    |
    */

    'primary_color' => '#FFD700',    // Yellow - for accents, interactive elements, highlights
    'secondary_color' => '#2E8B57',  // Green - for primary buttons, important text, backgrounds
    'tertiary_color' => '#007CA4',  // Blue - for accents, interactive elements, highlights
    /*
    |--------------------------------------------------------------------------
    | Additional Theme Colors
    |--------------------------------------------------------------------------
    |
    | These are derived colors and variations that can be used throughout
    | the application for consistency.
    |
    */

    'colors' => [
        'primary' => '#FFD700',
        'primary_dark' => '#E6C200',
        'primary_light' => '#FFF033',
        'primary_rgb' => '255, 215, 0',
        
        'secondary' => '#2E8B57',
        'secondary_dark' => '#1F5F3F',
        'secondary_light' => '#3FA76F',
        'secondary_rgb' => '46, 139, 87',
        
        'success' => '#28a745',
        'info' => '#17a2b8',
        'warning' => '#ffc107',
        'danger' => '#dc3545',
        'light' => '#f8f9fa',
        'dark' => '#343a40',
        'white' => '#ffffff',
        'black' => '#000000',
        
        'gray_100' => '#f8f9fa',
        'gray_200' => '#e9ecef',
        'gray_300' => '#dee2e6',
        'gray_400' => '#ced4da',
        'gray_500' => '#adb5bd',
        'gray_600' => '#6c757d',
        'gray_700' => '#495057',
        'gray_800' => '#343a40',
        'gray_900' => '#212529',
    ],

    /*
    |--------------------------------------------------------------------------
    | Typography
    |--------------------------------------------------------------------------
    |
    | Font families and typography settings for the application.
    |
    */

    'typography' => [
        'font_family_primary' => "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
        'font_family_secondary' => "'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
        'font_size_base' => '1rem',
        'line_height_base' => '1.5',
    ],

    /*
    |--------------------------------------------------------------------------
    | Layout Settings
    |--------------------------------------------------------------------------
    |
    | Common layout and spacing settings.
    |
    */

    'layout' => [
        'container_max_width' => '1200px',
        'border_radius' => '0.375rem',
        'border_radius_lg' => '0.5rem',
        'border_radius_xl' => '0.75rem',
        'box_shadow' => '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        'box_shadow_lg' => '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    ],
];
