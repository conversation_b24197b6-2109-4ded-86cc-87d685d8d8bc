<?php

namespace Database\Seeders;

use App\Models\Location;
use App\Models\Property;
use App\Models\PropertyImage;
use Illuminate\Database\Seeder;

class PropertySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $locations = Location::all();

        if ($locations->isEmpty()) {
            $this->command->error('No locations found. Please run LocationSeeder first.');
            return;
        }

        $properties = [
            [
                'name' => 'Luxury Villa with Ocean View',
                'slug' => 'luxury-villa-ocean-view-bali',
                'description' => 'Experience the ultimate luxury in this stunning villa overlooking the pristine beaches of Bali. Features include a private pool, spacious living areas, and breathtaking sunset views.',
                'address' => 'Jl. Pantai Berawa No. 123',
                'price_per_night' => 350,
                'guest_capacity' => 8,
                'bedrooms' => 4,
                'bathrooms' => 3,
                'amenities' => ['Private Pool', 'Ocean View', 'WiFi', 'Air Conditioning', 'Kitchen', 'Parking'],
                'featured' => true,
                'status' => 'active',
                'location' => 'Bali',
                'images' => [
                    'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=800&h=600&fit=crop',
                ]
            ],
            [
                'name' => 'Modern Tokyo Apartment',
                'slug' => 'modern-tokyo-apartment-shibuya',
                'description' => 'Stay in the heart of Tokyo in this modern apartment with easy access to Shibuya crossing and major attractions. Perfect for exploring the city.',
                'address' => '2-1-1 Shibuya, Shibuya City',
                'price_per_night' => 120,
                'guest_capacity' => 4,
                'bedrooms' => 2,
                'bathrooms' => 1,
                'amenities' => ['WiFi', 'Air Conditioning', 'Kitchen', 'Washer', 'Near Station'],
                'featured' => true,
                'status' => 'active',
                'location' => 'Tokyo',
                'images' => [
                    'https://images.unsplash.com/photo-1555636222-cae831e670b3?w=800&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop',
                ]
            ],
            [
                'name' => 'Charming Parisian Loft',
                'slug' => 'charming-parisian-loft-montmartre',
                'description' => 'Authentic Parisian experience in this charming loft located in the artistic Montmartre district. Walking distance to Sacré-Cœur and local cafés.',
                'address' => '15 Rue des Abbesses',
                'price_per_night' => 180,
                'guest_capacity' => 2,
                'bedrooms' => 1,
                'bathrooms' => 1,
                'amenities' => ['WiFi', 'Kitchen', 'Historic Building', 'City View'],
                'featured' => false,
                'status' => 'active',
                'location' => 'Paris',
                'images' => [
                    'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=800&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop',
                ]
            ],
            [
                'name' => 'Manhattan Penthouse',
                'slug' => 'manhattan-penthouse-central-park',
                'description' => 'Luxurious penthouse with stunning Central Park views. This exclusive property offers the finest amenities and prime location in Manhattan.',
                'address' => '1 Central Park West',
                'price_per_night' => 800,
                'guest_capacity' => 6,
                'bedrooms' => 3,
                'bathrooms' => 2,
                'amenities' => ['Central Park View', 'Doorman', 'Gym', 'WiFi', 'Kitchen', 'Balcony'],
                'featured' => true,
                'status' => 'active',
                'location' => 'New York',
                'images' => [
                    'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1560185007-cde436f6a4d0?w=800&h=600&fit=crop',
                ]
            ],
            [
                'name' => 'Cozy London Townhouse',
                'slug' => 'cozy-london-townhouse-notting-hill',
                'description' => 'Traditional Victorian townhouse in trendy Notting Hill. Experience London like a local in this beautifully restored home.',
                'address' => '42 Portobello Road',
                'price_per_night' => 250,
                'guest_capacity' => 5,
                'bedrooms' => 3,
                'bathrooms' => 2,
                'amenities' => ['WiFi', 'Kitchen', 'Garden', 'Fireplace', 'Historic'],
                'featured' => false,
                'status' => 'active',
                'location' => 'London',
                'images' => [
                    'https://images.unsplash.com/photo-1513694203232-719a280e022f?w=800&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1560185009-5bf9f2849488?w=800&h=600&fit=crop',
                ]
            ],
            [
                'name' => 'Barcelona Beach House',
                'slug' => 'barcelona-beach-house-barceloneta',
                'description' => 'Stunning beach house just steps from Barceloneta Beach. Enjoy Mediterranean living with modern comforts and sea views.',
                'address' => 'Passeig Marítim de la Barceloneta, 37',
                'price_per_night' => 200,
                'guest_capacity' => 6,
                'bedrooms' => 3,
                'bathrooms' => 2,
                'amenities' => ['Beach Access', 'Sea View', 'WiFi', 'Kitchen', 'Terrace', 'Air Conditioning'],
                'featured' => false,
                'status' => 'active',
                'location' => 'Barcelona',
                'images' => [
                    'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1560185007-cde436f6a4d0?w=800&h=600&fit=crop',
                ]
            ],
        ];

        foreach ($properties as $propertyData) {
            $location = $locations->where('name', $propertyData['location'])->first();

            if (!$location) {
                $this->command->warn("Location '{$propertyData['location']}' not found. Skipping property '{$propertyData['name']}'");
                continue;
            }

            $images = $propertyData['images'];
            unset($propertyData['images'], $propertyData['location']);

            $propertyData['location_id'] = $location->id;

            $property = Property::create($propertyData);

            // Add images
            foreach ($images as $index => $imageUrl) {
                PropertyImage::create([
                    'property_id' => $property->id,
                    'image_path' => $imageUrl,
                    'alt_text' => $property->name . ' - Image ' . ($index + 1),
                    'sort_order' => $index,
                    'is_primary' => $index === 0,
                ]);
            }
        }

        $this->command->info('Properties seeded successfully!');
    }
}
