<?php

namespace Database\Seeders;

use App\Models\Property;
use Illuminate\Database\Seeder;

class UpdatePropertiesCreditsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Update existing properties with credit values based on price ranges
        Property::whereNull('credits_per_night')->orWhere('credits_per_night', 0)->chunk(100, function ($properties) {
            foreach ($properties as $property) {
                // Calculate credits based on price per night
                $pricePerNight = $property->price_per_night;

                if ($pricePerNight <= 50) {
                    $credits = 1;
                } elseif ($pricePerNight <= 100) {
                    $credits = 2;
                } elseif ($pricePerNight <= 200) {
                    $credits = 3;
                } elseif ($pricePerNight <= 300) {
                    $credits = 4;
                } else {
                    $credits = 5;
                }

                $property->update(['credits_per_night' => $credits]);
            }
        });

        $this->command->info('Updated credits_per_night for all existing properties.');
    }
}
