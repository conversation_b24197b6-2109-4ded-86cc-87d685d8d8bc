<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\CreditTransaction;
use Illuminate\Database\Seeder;

class AddInitialCreditsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Give all existing users 10 initial credits
        $users = User::where('credits', 0)->get();

        foreach ($users as $user) {
            $user->update(['credits' => 10]);

            // Create a credit transaction record
            CreditTransaction::create([
                'user_id' => $user->id,
                'amount' => 10,
                'type' => 'credit',
                'description' => 'Welcome bonus - Initial credits',
            ]);
        }

        $this->command->info('Added 10 initial credits to ' . $users->count() . ' users.');
    }
}
