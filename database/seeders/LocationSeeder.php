<?php

namespace Database\Seeders;

use App\Models\Location;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class LocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $locations = [
            [
                'name' => 'Bali',
                'slug' => 'bali',
                'city' => 'Denpasar',
                'state' => 'Bali',
                'country' => 'Indonesia',
            ],
            [
                'name' => 'Tokyo',
                'slug' => 'tokyo',
                'city' => 'Tokyo',
                'state' => 'Tokyo',
                'country' => 'Japan',
            ],
            [
                'name' => 'Paris',
                'slug' => 'paris',
                'city' => 'Paris',
                'state' => 'Île-de-France',
                'country' => 'France',
            ],
            [
                'name' => 'New York',
                'slug' => 'new-york',
                'city' => 'New York',
                'state' => 'New York',
                'country' => 'United States',
            ],
            [
                'name' => 'London',
                'slug' => 'london',
                'city' => 'London',
                'state' => 'England',
                'country' => 'United Kingdom',
            ],
            [
                'name' => 'Barcelona',
                'slug' => 'barcelona',
                'city' => 'Barcelona',
                'state' => 'Catalonia',
                'country' => 'Spain',
            ],
        ];

        foreach ($locations as $location) {
            Location::create($location);
        }

        $this->command->info('Locations seeded successfully!');
    }
}
