<?php

use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\PropertyController as AdminPropertyController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\PropertyController;
use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

// Homepage
Route::get('/', [HomeController::class, 'index'])->name('home');

// Property routes
Route::get('/properties', [PropertyController::class, 'index'])->name('properties.search');
Route::get('/properties/{property:slug}', [PropertyController::class, 'show'])->name('properties.show');

// Location suggestions for autocomplete
Route::get('/api/location-suggestions', [HomeController::class, 'locationSuggestions'])->name('location.suggestions');

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Admin Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Admin Authentication Routes (not protected by admin middleware)
    Route::get('/login', [AdminController::class, 'showLogin'])->name('login');
    Route::post('/login', [AdminController::class, 'login']);

    // Protected Admin Routes
    Route::middleware('admin')->group(function () {
        Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
        Route::post('/logout', [AdminController::class, 'logout'])->name('logout');

        // Property Management
        Route::resource('properties', AdminPropertyController::class);
        Route::delete('/properties/{image}/delete-image', [AdminPropertyController::class, 'deleteImage'])->name('properties.delete-image');
        Route::patch('/properties/{image}/set-primary', [AdminPropertyController::class, 'setPrimaryImage'])->name('properties.set-primary');
    });
});

require __DIR__.'/auth.php';
