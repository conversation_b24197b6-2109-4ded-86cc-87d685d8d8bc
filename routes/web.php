<?php

use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\PropertyController as AdminPropertyController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\PropertyController;
use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

// Homepage
Route::get('/', [HomeController::class, 'index'])->name('home');

// Property routes
Route::get('/properties', [PropertyController::class, 'index'])->name('properties.search');
Route::get('/properties/{property:slug}', [PropertyController::class, 'show'])->name('properties.show');

// Location suggestions for autocomplete
Route::get('/api/location-suggestions', [HomeController::class, 'locationSuggestions'])->name('location.suggestions');

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Booking routes
    Route::resource('bookings', BookingController::class);

    // Credit routes
    Route::get('/credits', [App\Http\Controllers\CreditController::class, 'index'])->name('credits.index');
});

// Admin Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Admin Authentication Routes (not protected by admin middleware)
    Route::get('/login', [AdminController::class, 'showLogin'])->name('login');
    Route::post('/login', [AdminController::class, 'login']);

    // Protected Admin Routes
    Route::middleware('admin')->group(function () {
        Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
        Route::post('/logout', [AdminController::class, 'logout'])->name('logout');

        // Property Management
        Route::resource('properties', AdminPropertyController::class);
        Route::delete('/properties/{image}/delete-image', [AdminPropertyController::class, 'deleteImage'])->name('properties.delete-image');
        Route::patch('/properties/{image}/set-primary', [AdminPropertyController::class, 'setPrimaryImage'])->name('properties.set-primary');

        // Location Management
        Route::resource('locations', App\Http\Controllers\Admin\LocationController::class);
        Route::get('/locations/{location}/analytics', [App\Http\Controllers\Admin\LocationController::class, 'analytics'])->name('locations.analytics');

        // User Management
        Route::resource('users', App\Http\Controllers\Admin\UserController::class);
        Route::post('/users/{user}/manage-credits', [App\Http\Controllers\Admin\UserController::class, 'manageCredits'])->name('users.manage-credits');
        Route::get('/users-export', [App\Http\Controllers\Admin\UserController::class, 'export'])->name('users.export');

        // Booking Management
        Route::resource('bookings', App\Http\Controllers\Admin\BookingController::class)->except(['create', 'store', 'destroy']);
        Route::patch('/bookings/{booking}/status', [App\Http\Controllers\Admin\BookingController::class, 'updateStatus'])->name('bookings.update-status');
        Route::post('/bookings/bulk-update', [App\Http\Controllers\Admin\BookingController::class, 'bulkUpdate'])->name('bookings.bulk-update');
        Route::get('/bookings-export', [App\Http\Controllers\Admin\BookingController::class, 'export'])->name('bookings.export');
        Route::get('/bookings-analytics', [App\Http\Controllers\Admin\BookingController::class, 'analytics'])->name('bookings.analytics');

        // Reviews Management
        Route::resource('reviews', App\Http\Controllers\Admin\ReviewController::class)->except(['create', 'store', 'edit']);
        Route::patch('/reviews/{review}/status', [App\Http\Controllers\Admin\ReviewController::class, 'updateStatus'])->name('reviews.update-status');
        Route::post('/reviews/bulk-update', [App\Http\Controllers\Admin\ReviewController::class, 'bulkUpdate'])->name('reviews.bulk-update');

        // Credit System Administration
        Route::prefix('credits')->name('credits.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\CreditController::class, 'index'])->name('index');
            Route::get('/transactions', [App\Http\Controllers\Admin\CreditController::class, 'transactions'])->name('transactions');
            Route::get('/bulk-operations', [App\Http\Controllers\Admin\CreditController::class, 'bulkOperations'])->name('bulk-operations');
            Route::post('/bulk-operations', [App\Http\Controllers\Admin\CreditController::class, 'processBulkOperations'])->name('process-bulk-operations');
            Route::get('/analytics', [App\Http\Controllers\Admin\CreditController::class, 'analytics'])->name('analytics');
            Route::get('/export', [App\Http\Controllers\Admin\CreditController::class, 'exportTransactions'])->name('export');
            Route::get('/reports', [App\Http\Controllers\Admin\CreditController::class, 'reports'])->name('reports');
        });
    });
});

require __DIR__.'/auth.php';
