/* Import theme variables first */
@import 'theme.css';

/* Import Bootstrap */
@import 'bootstrap/dist/css/bootstrap.min.css';

/* Import FontAwesome */
@import '@fortawesome/fontawesome-free/css/all.min.css';

/* Import Flatpickr */
@import 'flatpickr/dist/flatpickr.min.css';

/* Keep Tailwind for utility classes */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Flatpickr theme to match our colors */
.flatpickr-calendar {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
}

.flatpickr-day.selected {
    background: var(--color-secondary);
    border-color: var(--color-secondary);
}

.flatpickr-day.selected:hover {
    background: var(--color-secondary-dark);
    border-color: var(--color-secondary-dark);
}

.flatpickr-day:hover {
    background: var(--color-primary);
    color: var(--color-gray-900);
}

.flatpickr-months .flatpickr-month {
    background: var(--color-secondary);
    color: var(--color-white);
}

.flatpickr-current-month .flatpickr-monthDropdown-months {
    background: var(--color-secondary);
}

/* Custom SweetAlert2 styling */
.swal2-popup {
    border-radius: var(--border-radius-lg);
}

.swal2-confirm {
    background-color: var(--color-secondary) !important;
}

.swal2-confirm:hover {
    background-color: var(--color-secondary-dark) !important;
}

/* Additional responsive utilities */
@media (max-width: 576px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .btn {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
    }
}

/* Loading spinner */
.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner-overlay .spinner-border {
    width: 3rem;
    height: 3rem;
    color: var(--color-primary);
}
