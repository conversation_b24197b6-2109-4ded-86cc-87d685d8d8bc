/* Oasis Homestay Theme Variables */
:root {
    /* Primary Colors */
    --color-primary: #FFD700;
    --color-primary-dark: #E6C200;
    --color-primary-light: #FFF033;
    --color-primary-rgb: 255, 215, 0;
    
    /* Secondary Colors */
    --color-secondary: #2E8B57;
    --color-secondary-dark: #1F5F3F;
    --color-secondary-light: #3FA76F;
    --color-secondary-rgb: 46, 139, 87;
    
    /* Status Colors */
    --color-success: #28a745;
    --color-info: #17a2b8;
    --color-warning: #ffc107;
    --color-danger: #dc3545;
    --color-light: #f8f9fa;
    --color-dark: #343a40;
    --color-white: #ffffff;
    --color-black: #000000;
    
    /* Gray Scale */
    --color-gray-100: #f8f9fa;
    --color-gray-200: #e9ecef;
    --color-gray-300: #dee2e6;
    --color-gray-400: #ced4da;
    --color-gray-500: #adb5bd;
    --color-gray-600: #6c757d;
    --color-gray-700: #495057;
    --color-gray-800: #343a40;
    --color-gray-900: #212529;
    
    /* Typography */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-family-secondary: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-size-base: 1rem;
    --line-height-base: 1.5;
    
    /* Layout */
    --container-max-width: 1200px;
    --border-radius: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 0.75rem;
    --box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Base Styles */
body {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
    color: var(--color-gray-900);
}

/* Button Styles */
.btn-primary {
    background-color: var(--color-secondary);
    border-color: var(--color-secondary);
    color: var(--color-white);
}

.btn-primary:hover {
    background-color: var(--color-secondary-dark);
    border-color: var(--color-secondary-dark);
}

.btn-secondary {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
    color: var(--color-gray-900);
}

.btn-secondary:hover {
    background-color: var(--color-primary-dark);
    border-color: var(--color-primary-dark);
}

/* Link Styles */
a {
    color: var(--color-secondary);
    text-decoration: none;
}

a:hover {
    color: var(--color-secondary-dark);
    text-decoration: underline;
}

/* Form Styles */
.form-control:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--color-primary-rgb), 0.25);
}

.form-check-input:checked {
    background-color: var(--color-secondary);
    border-color: var(--color-secondary);
}

/* Navigation Styles */
.navbar-brand {
    color: var(--color-secondary) !important;
    font-weight: 600;
}

.nav-link {
    color: var(--color-gray-700) !important;
}

.nav-link:hover {
    color: var(--color-secondary) !important;
}

.nav-link.active {
    color: var(--color-secondary) !important;
}

/* Card Styles */
.card {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    border: 1px solid var(--color-gray-200);
}

.card-header {
    background-color: var(--color-gray-50);
    border-bottom: 1px solid var(--color-gray-200);
}

/* Alert Styles */
.alert-primary {
    background-color: rgba(var(--color-primary-rgb), 0.1);
    border-color: var(--color-primary);
    color: var(--color-gray-900);
}

.alert-secondary {
    background-color: rgba(var(--color-secondary-rgb), 0.1);
    border-color: var(--color-secondary);
    color: var(--color-white);
}

/* Badge Styles */
.badge-primary {
    background-color: var(--color-primary);
    color: var(--color-gray-900);
}

.badge-secondary {
    background-color: var(--color-secondary);
    color: var(--color-white);
}

/* Utility Classes */
.text-primary {
    color: var(--color-primary) !important;
}

.text-secondary {
    color: var(--color-secondary) !important;
}

.bg-primary {
    background-color: var(--color-primary) !important;
}

.bg-secondary {
    background-color: var(--color-secondary) !important;
}

.border-primary {
    border-color: var(--color-primary) !important;
}

.border-secondary {
    border-color: var(--color-secondary) !important;
}

/* Custom Component Styles */
.hero-section {
    background: linear-gradient(135deg, var(--color-secondary), var(--color-secondary-light));
    color: var(--color-white);
}

.property-card {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.property-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.search-bar {
    background-color: var(--color-white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--box-shadow-lg);
}

.price-highlight {
    color: var(--color-secondary);
    font-weight: 600;
}

.rating-stars {
    color: var(--color-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        max-width: 100%;
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-primary {
    color: var(--color-primary);
}

.spinner-border-secondary {
    color: var(--color-secondary);
}
