import './bootstrap';

// Import Bootstrap
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap';

// Import FontAwesome
import '@fortawesome/fontawesome-free/css/all.min.css';

// Import Flatpickr
import flatpickr from 'flatpickr';
import 'flatpickr/dist/flatpickr.min.css';

// Import SweetAlert2
import Swal from 'sweetalert2';

// Import Alpine.js
import Alpine from 'alpinejs';

// Make libraries globally available
window.flatpickr = flatpickr;
window.Swal = Swal;
window.Alpine = Alpine;

// Initialize Alpine.js
Alpine.start();

// Initialize Flatpickr for date inputs
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all date picker inputs
    const dateInputs = document.querySelectorAll('.date-picker');
    dateInputs.forEach(input => {
        flatpickr(input, {
            dateFormat: 'Y-m-d',
            minDate: 'today',
            theme: 'material_blue'
        });
    });

    // Initialize date range pickers
    const dateRangeInputs = document.querySelectorAll('.date-range-picker');
    dateRangeInputs.forEach(input => {
        flatpickr(input, {
            mode: 'range',
            dateFormat: 'Y-m-d',
            minDate: 'today',
            theme: 'material_blue'
        });
    });

    // Initialize check-in/check-out date pickers
    const checkInInput = document.querySelector('#check_in_date');
    const checkOutInput = document.querySelector('#check_out_date');

    if (checkInInput && checkOutInput) {
        const checkInPicker = flatpickr(checkInInput, {
            dateFormat: 'Y-m-d',
            minDate: 'today',
            theme: 'material_blue',
            onChange: function(selectedDates) {
                if (selectedDates.length > 0) {
                    const minCheckOut = new Date(selectedDates[0]);
                    minCheckOut.setDate(minCheckOut.getDate() + 1);
                    checkOutPicker.set('minDate', minCheckOut);
                }
            }
        });

        const checkOutPicker = flatpickr(checkOutInput, {
            dateFormat: 'Y-m-d',
            minDate: 'today',
            theme: 'material_blue'
        });
    }
});

// Global SweetAlert2 configurations
window.showAlert = function(type, title, text) {
    Swal.fire({
        icon: type,
        title: title,
        text: text,
        confirmButtonColor: '#2E8B57',
        cancelButtonColor: '#dc3545'
    });
};

window.showConfirm = function(title, text, callback) {
    Swal.fire({
        title: title,
        text: text,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#2E8B57',
        cancelButtonColor: '#dc3545',
        confirmButtonText: 'Yes, proceed!',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed && callback) {
            callback();
        }
    });
};

window.showSuccess = function(title, text) {
    Swal.fire({
        icon: 'success',
        title: title,
        text: text,
        confirmButtonColor: '#2E8B57'
    });
};

window.showError = function(title, text) {
    Swal.fire({
        icon: 'error',
        title: title,
        text: text,
        confirmButtonColor: '#2E8B57'
    });
};
