@extends('layouts.frontend')

@section('title', 'Welcome to Oasis Homestay')

@section('content')
<!-- Hero Section -->
<section class="hero-section position-relative overflow-hidden" style="min-height: 70vh; background: linear-gradient(135deg, @primaryColor() 0%, @secondaryColor() 100%);">
    <div class="container h-100">
        <div class="row h-100 align-items-center">
            <div class="col-lg-8 mx-auto text-center text-dark">
                <h1 class="display-3 fw-bold mb-4">
                    Find Your Perfect <span style="color: @secondaryColor();">Homestay</span>
                </h1>
                <p class="lead mb-5">
                    Discover unique accommodations and unforgettable experiences around the world
                </p>
                
                <!-- Search Form -->
                <div class="card shadow-lg border-0 p-4">
                    <form action="{{ route('properties.search') }}" method="GET" class="search-form">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="form-floating">
                                    <input type="text" 
                                           class="form-control" 
                                           id="location" 
                                           name="location" 
                                           placeholder="Where are you going?"
                                           autocomplete="off">
                                    <label for="location">
                                        <i class="fas fa-map-marker-alt me-2"></i>Where are you going?
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-floating">
                                    <input type="date" 
                                           class="form-control" 
                                           id="check_in" 
                                           name="check_in"
                                           min="{{ date('Y-m-d') }}">
                                    <label for="check_in">
                                        <i class="fas fa-calendar me-2"></i>Check-in
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-floating">
                                    <input type="date" 
                                           class="form-control" 
                                           id="check_out" 
                                           name="check_out"
                                           min="{{ date('Y-m-d', strtotime('+1 day')) }}">
                                    <label for="check_out">
                                        <i class="fas fa-calendar me-2"></i>Check-out
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-floating">
                                    <select class="form-select" id="guests" name="guests">
                                        <option value="1">1 Guest</option>
                                        <option value="2" selected>2 Guests</option>
                                        <option value="3">3 Guests</option>
                                        <option value="4">4 Guests</option>
                                        <option value="5">5 Guests</option>
                                        <option value="6">6+ Guests</option>
                                    </select>
                                    <label for="guests">
                                        <i class="fas fa-users me-2"></i>Guests
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary btn-lg px-5">
                                    <i class="fas fa-search me-2"></i>Search Properties
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Properties Section -->
@if($featuredProperties->count() > 0)
<section class="py-5">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="display-5 fw-bold mb-3" style="color: @secondaryColor();">
                    Featured Properties
                </h2>
                <p class="lead text-muted">
                    Handpicked accommodations for your perfect stay
                </p>
            </div>
        </div>
        
        <div class="row">
            @foreach($featuredProperties as $property)
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card property-card h-100 shadow-sm border-0">
                    <div class="position-relative">
                        <img src="{{ $property->primary_image_url }}" 
                             class="card-img-top" 
                             alt="{{ $property->name }}"
                             style="height: 250px; object-fit: cover;">
                        <div class="position-absolute top-0 end-0 m-3">
                            <span class="badge bg-primary">Featured</span>
                        </div>
                        <div class="position-absolute bottom-0 start-0 m-3">
                            <span class="badge bg-dark bg-opacity-75">
                                <i class="fas fa-star text-warning me-1"></i>
                                {{ number_format($property->average_rating, 1) }}
                                ({{ $property->reviews_count }} reviews)
                            </span>
                        </div>
                    </div>
                    <div class="card-body d-flex flex-column">
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {{ $property->location->name }}
                            </small>
                        </div>
                        <h5 class="card-title">{{ $property->name }}</h5>
                        <p class="card-text text-muted flex-grow-1">
                            {{ Str::limit($property->description, 100) }}
                        </p>
                        <div class="d-flex justify-content-between align-items-center mt-auto">
                            <div>
                                <span class="h5 mb-0" style="color: @primaryColor();">
                                    ${{ number_format($property->price_per_night) }}
                                </span>
                                <small class="text-muted">/night</small>
                            </div>
                            <a href="{{ route('properties.show', $property->slug) }}" 
                               class="btn btn-outline-primary btn-sm">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        
        <div class="text-center mt-4">
            <a href="{{ route('properties.search') }}" class="btn btn-primary btn-lg">
                View All Properties
            </a>
        </div>
    </div>
</section>
@endif

<!-- Popular Locations Section -->
@if($popularLocations->count() > 0)
<section class="py-5 bg-light">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="display-5 fw-bold mb-3" style="color: @secondaryColor();">
                    Popular Destinations
                </h2>
                <p class="lead text-muted">
                    Explore our most loved locations
                </p>
            </div>
        </div>
        
        <div class="row">
            @foreach($popularLocations as $location)
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card location-card h-100 shadow-sm border-0">
                    <div class="position-relative">
                        <img src="{{ $location->image_url }}" 
                             class="card-img-top" 
                             alt="{{ $location->name }}"
                             style="height: 200px; object-fit: cover;">
                        <div class="card-img-overlay d-flex align-items-end">
                            <div class="text-white">
                                <h5 class="card-title mb-1">{{ $location->name }}</h5>
                                <small>{{ $location->properties_count }} properties</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Recent Reviews Section -->
@if($recentReviews->count() > 0)
<section class="py-5">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="display-5 fw-bold mb-3" style="color: @secondaryColor();">
                    What Our Guests Say
                </h2>
                <p class="lead text-muted">
                    Real experiences from real travelers
                </p>
            </div>
        </div>
        
        <div class="row">
            @foreach($recentReviews->take(3) as $review)
            <div class="col-lg-4 mb-4">
                <div class="card review-card h-100 shadow-sm border-0">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <img src="{{ $review->user->avatar_url }}" 
                                 class="rounded-circle me-3" 
                                 width="50" height="50"
                                 alt="{{ $review->user->name }}">
                            <div>
                                <h6 class="mb-0">{{ $review->user->name }}</h6>
                                <div class="text-warning">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star {{ $i <= $review->rating ? '' : 'text-muted' }}"></i>
                                    @endfor
                                </div>
                            </div>
                        </div>
                        <p class="card-text">
                            "{{ Str::limit($review->comment, 150) }}"
                        </p>
                        <small class="text-muted">
                            {{ $review->property->name }} • {{ $review->created_at->diffForHumans() }}
                        </small>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Call to Action Section -->
<section class="py-5" style="background: linear-gradient(135deg, @secondaryColor() 0%, @primaryColor() 100%);">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center text-dark">
                <h2 class="display-5 fw-bold mb-4">
                    Ready to Start Your Journey?
                </h2>
                <p class="lead mb-4">
                    Join thousands of travelers who have found their perfect homestay with us
                </p>
                <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                    <a href="{{ route('properties.search') }}" class="btn btn-dark btn-lg">
                        <i class="fas fa-search me-2"></i>Browse Properties
                    </a>
                    @guest
                    <a href="{{ route('register') }}" class="btn btn-outline-dark btn-lg">
                        <i class="fas fa-user-plus me-2"></i>Sign Up Today
                    </a>
                    @endguest
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.hero-section {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="0,0 1000,100 1000,0"/></svg>');
    background-size: cover;
}

.property-card:hover {
    transform: translateY(-5px);
    transition: transform 0.3s ease;
}

.location-card:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

.review-card {
    border-left: 4px solid @primaryColor();
}

.btn-primary {
    background-color: @secondaryColor();
    border-color: @secondaryColor();
}

.btn-primary:hover {
    background-color: #236B47;
    border-color: #236B47;
}

.btn-outline-primary {
    color: @secondaryColor();
    border-color: @secondaryColor();
}

.btn-outline-primary:hover {
    background-color: @secondaryColor();
    border-color: @secondaryColor();
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Location autocomplete
    const locationInput = document.getElementById('location');
    if (locationInput) {
        // Initialize autocomplete functionality
        setupLocationAutocomplete(locationInput);
    }
    
    // Date validation
    const checkInInput = document.getElementById('check_in');
    const checkOutInput = document.getElementById('check_out');
    
    if (checkInInput && checkOutInput) {
        checkInInput.addEventListener('change', function() {
            const checkInDate = new Date(this.value);
            const nextDay = new Date(checkInDate);
            nextDay.setDate(nextDay.getDate() + 1);
            checkOutInput.min = nextDay.toISOString().split('T')[0];
            
            if (checkOutInput.value && new Date(checkOutInput.value) <= checkInDate) {
                checkOutInput.value = nextDay.toISOString().split('T')[0];
            }
        });
    }
});

function setupLocationAutocomplete(input) {
    let timeout;
    let suggestions = [];
    
    // Create suggestions container
    const suggestionsContainer = document.createElement('div');
    suggestionsContainer.className = 'position-absolute bg-white border rounded shadow-sm w-100';
    suggestionsContainer.style.zIndex = '1000';
    suggestionsContainer.style.top = '100%';
    suggestionsContainer.style.display = 'none';
    
    input.parentElement.style.position = 'relative';
    input.parentElement.appendChild(suggestionsContainer);
    
    input.addEventListener('input', function() {
        clearTimeout(timeout);
        const term = this.value.trim();
        
        if (term.length < 2) {
            suggestionsContainer.style.display = 'none';
            return;
        }
        
        timeout = setTimeout(() => {
            fetch(`{{ route('location.suggestions') }}?term=${encodeURIComponent(term)}`)
                .then(response => response.json())
                .then(data => {
                    suggestions = data;
                    displaySuggestions(data, suggestionsContainer, input);
                })
                .catch(error => console.error('Error:', error));
        }, 300);
    });
    
    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (!input.parentElement.contains(e.target)) {
            suggestionsContainer.style.display = 'none';
        }
    });
}

function displaySuggestions(suggestions, container, input) {
    container.innerHTML = '';
    
    if (suggestions.length === 0) {
        container.style.display = 'none';
        return;
    }
    
    suggestions.forEach(suggestion => {
        const item = document.createElement('div');
        item.className = 'p-2 cursor-pointer';
        item.style.cursor = 'pointer';
        item.textContent = suggestion.label;
        
        item.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.backgroundColor = 'transparent';
        });
        
        item.addEventListener('click', function() {
            input.value = suggestion.value;
            container.style.display = 'none';
        });
        
        container.appendChild(item);
    });
    
    container.style.display = 'block';
}
</script>
@endsection
