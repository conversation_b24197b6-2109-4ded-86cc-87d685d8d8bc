@extends('layouts.frontend')

@section('title', 'My Bookings')

@section('content')
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0" style="color: @secondaryColor();">
                    <i class="fas fa-calendar-check me-2"></i>My Bookings
                </h2>
                <a href="{{ route('properties.search') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>New Booking
                </a>
            </div>

            @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            @endif

            @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            @endif

            @if($bookings->count() > 0)
                @foreach($bookings as $booking)
                <div class="card mb-4 shadow-sm border-0">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <img src="{{ $booking->property->primary_image_url }}" 
                                     class="img-fluid rounded" 
                                     alt="{{ $booking->property->name }}"
                                     style="height: 150px; width: 100%; object-fit: cover;">
                            </div>
                            <div class="col-md-6">
                                <h5 class="card-title mb-2">{{ $booking->property->name }}</h5>
                                <p class="text-muted mb-2">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    {{ $booking->property->location->name }}
                                </p>
                                <div class="row">
                                    <div class="col-sm-6">
                                        <small class="text-muted">Check-in:</small><br>
                                        <strong>{{ $booking->check_in_date->format('M d, Y') }}</strong>
                                    </div>
                                    <div class="col-sm-6">
                                        <small class="text-muted">Check-out:</small><br>
                                        <strong>{{ $booking->check_out_date->format('M d, Y') }}</strong>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-sm-6">
                                        <small class="text-muted">Guests:</small> {{ $booking->guests }}
                                    </div>
                                    <div class="col-sm-6">
                                        <small class="text-muted">Nights:</small> {{ $booking->nights }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 text-end">
                                <div class="mb-2">
                                    @if($booking->status === 'confirmed')
                                        <span class="badge bg-success">Confirmed</span>
                                    @elseif($booking->status === 'pending')
                                        <span class="badge bg-warning">Pending</span>
                                    @elseif($booking->status === 'cancelled')
                                        <span class="badge bg-danger">Cancelled</span>
                                    @else
                                        <span class="badge bg-secondary">{{ ucfirst($booking->status) }}</span>
                                    @endif
                                </div>
                                <div class="mb-2">
                                    <small class="text-muted">Reference:</small><br>
                                    <strong>{{ $booking->booking_reference }}</strong>
                                </div>
                                <div class="mb-3">
                                    <small class="text-muted">Credits Used:</small><br>
                                    <strong class="text-success">{{ $booking->nights }}</strong>
                                </div>
                                
                                <div class="d-flex flex-column gap-2">
                                    <a href="{{ route('bookings.show', $booking) }}" 
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i>View Details
                                    </a>
                                    
                                    @if($booking->status === 'confirmed' && $booking->check_in_date->isFuture())
                                        <a href="{{ route('bookings.edit', $booking) }}" 
                                           class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-edit me-1"></i>Modify
                                        </a>
                                        
                                        <form action="{{ route('bookings.destroy', $booking) }}" 
                                              method="POST" 
                                              class="d-inline"
                                              onsubmit="return confirmCancellation()">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                                <i class="fas fa-times me-1"></i>Cancel
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $bookings->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-calendar-times fa-4x text-muted"></i>
                    </div>
                    <h4 class="text-muted mb-3">No bookings yet</h4>
                    <p class="text-muted mb-4">Start exploring our amazing properties and make your first booking!</p>
                    <a href="{{ route('properties.search') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-search me-2"></i>Browse Properties
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
.btn-primary {
    background-color: @secondaryColor();
    border-color: @secondaryColor();
}

.btn-primary:hover {
    background-color: #236B47;
    border-color: #236B47;
}

.btn-outline-primary {
    color: @secondaryColor();
    border-color: @secondaryColor();
}

.btn-outline-primary:hover {
    background-color: @secondaryColor();
    border-color: @secondaryColor();
}

.card {
    border-radius: 10px;
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
}
</style>

<script>
function confirmCancellation() {
    return confirm('Are you sure you want to cancel this booking? Your credits will be refunded.');
}
</script>
@endsection
