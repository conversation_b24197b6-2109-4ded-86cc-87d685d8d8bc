@extends('layouts.frontend')

@section('title', 'Booking Confirmation - ' . $booking->booking_reference)

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Success Alert -->
            @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            @endif

            <!-- Booking Confirmation Card -->
            <div class="card shadow-lg border-0">
                <div class="card-header text-center py-4" style="background: linear-gradient(135deg, @secondaryColor() 0%, @primaryColor() 100%);">
                    <div class="text-dark">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <h2 class="mb-0">Booking Confirmed!</h2>
                        <p class="mb-0">Reference: <strong>{{ $booking->booking_reference }}</strong></p>
                    </div>
                </div>
                
                <div class="card-body p-4">
                    <!-- Property Information -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <img src="{{ $booking->property->primary_image_url }}" 
                                 class="img-fluid rounded" 
                                 alt="{{ $booking->property->name }}"
                                 style="height: 200px; width: 100%; object-fit: cover;">
                        </div>
                        <div class="col-md-8">
                            <h4 class="mb-2">{{ $booking->property->name }}</h4>
                            <p class="text-muted mb-2">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                {{ $booking->property->location->name }}
                            </p>
                            <div class="row">
                                <div class="col-sm-6">
                                    <p class="mb-1"><strong>Check-in:</strong></p>
                                    <p class="text-muted">{{ $booking->check_in_date->format('M d, Y') }}</p>
                                </div>
                                <div class="col-sm-6">
                                    <p class="mb-1"><strong>Check-out:</strong></p>
                                    <p class="text-muted">{{ $booking->check_out_date->format('M d, Y') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Booking Details -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light border-0 h-100">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-info-circle me-2" style="color: @secondaryColor();"></i>
                                        Booking Details
                                    </h5>
                                    <ul class="list-unstyled mb-0">
                                        <li class="mb-2">
                                            <strong>Guests:</strong> {{ $booking->guests }}
                                        </li>
                                        <li class="mb-2">
                                            <strong>Nights:</strong> {{ $booking->nights }}
                                        </li>
                                        <li class="mb-2">
                                            <strong>Status:</strong> 
                                            <span class="badge bg-success">{{ ucfirst($booking->status) }}</span>
                                        </li>
                                        <li class="mb-2">
                                            <strong>Credits Used:</strong> {{ $booking->nights }}
                                        </li>
                                        @if($booking->special_requests)
                                        <li class="mb-2">
                                            <strong>Special Requests:</strong><br>
                                            <small class="text-muted">{{ $booking->special_requests }}</small>
                                        </li>
                                        @endif
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card bg-light border-0 h-100">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-dollar-sign me-2" style="color: @primaryColor();"></i>
                                        Payment Summary
                                    </h5>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>${{ number_format($booking->property->price_per_night) }} x {{ $booking->nights }} nights:</span>
                                        <span>${{ number_format($booking->total_price) }}</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Credits Used:</span>
                                        <span class="text-success">{{ $booking->nights }} credits</span>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between fw-bold">
                                        <span>Total Paid:</span>
                                        <span style="color: @secondaryColor();">Credits Only</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex flex-wrap gap-3 justify-content-center">
                                <a href="{{ route('bookings.index') }}" class="btn btn-primary">
                                    <i class="fas fa-list me-2"></i>View All Bookings
                                </a>
                                <a href="{{ route('home') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-home me-2"></i>Back to Home
                                </a>
                                @if($booking->status === 'confirmed' && $booking->check_in_date->isFuture())
                                <a href="{{ route('bookings.edit', $booking) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-edit me-2"></i>Modify Booking
                                </a>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Important Information -->
                    <div class="alert alert-info mt-4" role="alert">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>Important Information
                        </h6>
                        <ul class="mb-0">
                            <li>Please arrive at the property between 3:00 PM - 10:00 PM on your check-in date</li>
                            <li>Check-out time is 11:00 AM</li>
                            <li>Contact the property directly for any special arrangements</li>
                            <li>You can modify or cancel this booking up to 24 hours before check-in</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.btn-primary {
    background-color: @secondaryColor();
    border-color: @secondaryColor();
}

.btn-primary:hover {
    background-color: #236B47;
    border-color: #236B47;
}

.btn-outline-primary {
    color: @secondaryColor();
    border-color: @secondaryColor();
}

.btn-outline-primary:hover {
    background-color: @secondaryColor();
    border-color: @secondaryColor();
}

.card {
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}
</style>
@endsection
