@extends('admin.layouts.app')

@section('title', 'Credit System Administration')
@section('page-title', 'Credit System')

@section('content')
<div class="row mb-4">
    <div class="col-md-8">
        <h4 class="mb-0" style="color: @secondaryColor();">
            <i class="fas fa-coins me-2"></i>Credit System Administration
        </h4>
        <p class="text-muted">Manage and monitor the credit-based booking system</p>
    </div>
    <div class="col-md-4 text-end">
        <div class="btn-group">
            <a href="{{ route('admin.credits.bulk-operations') }}" class="btn btn-primary">
                <i class="fas fa-tasks me-1"></i>Bulk Operations
            </a>
            <a href="{{ route('admin.credits.transactions') }}" class="btn btn-outline-secondary">
                <i class="fas fa-list me-1"></i>All Transactions
            </a>
            <a href="{{ route('admin.credits.reports') }}" class="btn btn-outline-info">
                <i class="fas fa-chart-line me-1"></i>Reports
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-2 mb-3">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, @primaryColor() 0%, #FFE55C 100%);">
            <div class="card-body text-center p-3">
                <i class="fas fa-plus-circle fa-2x text-dark mb-2"></i>
                <h5 class="fw-bold text-dark mb-0">{{ number_format($stats['total_credits_issued']) }}</h5>
                <small class="text-dark">Credits Issued</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #dc3545 0%, #f8d7da 100%);">
            <div class="card-body text-center p-3">
                <i class="fas fa-minus-circle fa-2x text-white mb-2"></i>
                <h5 class="fw-bold text-white mb-0">{{ number_format($stats['total_credits_used']) }}</h5>
                <small class="text-white">Credits Used</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, @secondaryColor() 0%, #90EE90 100%);">
            <div class="card-body text-center p-3">
                <i class="fas fa-wallet fa-2x text-white mb-2"></i>
                <h5 class="fw-bold text-white mb-0">{{ number_format($stats['credits_in_circulation']) }}</h5>
                <small class="text-white">In Circulation</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #17a2b8 0%, #7dd3fc 100%);">
            <div class="card-body text-center p-3">
                <i class="fas fa-users fa-2x text-white mb-2"></i>
                <h5 class="fw-bold text-white mb-0">{{ number_format($stats['active_users_with_credits']) }}</h5>
                <small class="text-white">Active Users</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #6f42c1 0%, #a855f7 100%);">
            <div class="card-body text-center p-3">
                <i class="fas fa-exchange-alt fa-2x text-white mb-2"></i>
                <h5 class="fw-bold text-white mb-0">{{ number_format($stats['total_transactions']) }}</h5>
                <small class="text-white">Transactions</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #ffc107 0%, #fff3cd 100%);">
            <div class="card-body text-center p-3">
                <i class="fas fa-calculator fa-2x text-dark mb-2"></i>
                <h5 class="fw-bold text-dark mb-0">{{ number_format($stats['average_credits_per_user'], 1) }}</h5>
                <small class="text-dark">Avg per User</small>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Credit Flow Chart -->
    <div class="col-lg-8 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <h6 class="mb-0">Monthly Credit Flow</h6>
            </div>
            <div class="card-body">
                <canvas id="creditFlowChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <!-- Top Users by Credits -->
    <div class="col-lg-4 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <h6 class="mb-0">Top Users by Credits</h6>
            </div>
            <div class="card-body p-0">
                @if($topUsers->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($topUsers as $index => $user)
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <span class="badge bg-{{ $index < 3 ? 'warning' : 'secondary' }} me-2">{{ $index + 1 }}</span>
                                <img src="{{ $user->avatar_url }}" 
                                     class="rounded-circle me-2" 
                                     width="24" height="24" 
                                     alt="{{ $user->name }}">
                                <div>
                                    <div class="fw-medium">{{ Str::limit($user->name, 20) }}</div>
                                    <small class="text-muted">{{ Str::limit($user->email, 25) }}</small>
                                </div>
                            </div>
                            <span class="badge bg-primary fs-6">{{ $user->credits }}</span>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No users with credits found</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Recent Transactions -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-0 py-3">
        <div class="d-flex justify-content-between align-items-center">
            <h6 class="mb-0">Recent Transactions</h6>
            <a href="{{ route('admin.credits.transactions') }}" class="btn btn-sm btn-outline-primary">
                View All <i class="fas fa-arrow-right ms-1"></i>
            </a>
        </div>
    </div>
    <div class="card-body p-0">
        @if($recentTransactions->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>User</th>
                            <th>Type</th>
                            <th>Amount</th>
                            <th>Description</th>
                            <th>Reference</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($recentTransactions as $transaction)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <img src="{{ $transaction->user->avatar_url }}" 
                                         class="rounded-circle me-2" 
                                         width="24" height="24" 
                                         alt="{{ $transaction->user->name }}">
                                    <div>
                                        <div class="fw-medium">{{ Str::limit($transaction->user->name, 20) }}</div>
                                        <small class="text-muted">{{ Str::limit($transaction->user->email, 25) }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{{ $transaction->type === 'credit' ? 'success' : 'danger' }}">
                                    <i class="fas fa-{{ $transaction->type === 'credit' ? 'plus' : 'minus' }} me-1"></i>
                                    {{ ucfirst($transaction->type) }}
                                </span>
                            </td>
                            <td>
                                <span class="fw-bold text-{{ $transaction->type === 'credit' ? 'success' : 'danger' }}">
                                    {{ $transaction->type === 'credit' ? '+' : '-' }}{{ $transaction->amount }}
                                </span>
                            </td>
                            <td>{{ Str::limit($transaction->description, 40) }}</td>
                            <td>
                                <span class="badge bg-light text-dark">{{ $transaction->reference_type }}</span>
                            </td>
                            <td>
                                <div>{{ $transaction->created_at->format('M d, Y') }}</div>
                                <small class="text-muted">{{ $transaction->created_at->format('H:i') }}</small>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No transactions found</h5>
                <p class="text-muted">Credit transactions will appear here once users start using the system.</p>
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Credit Flow Chart
    const ctx = document.getElementById('creditFlowChart').getContext('2d');
    const monthlyTrends = @json($monthlyTrends);
    
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const labels = [];
    const issuedData = [];
    const usedData = [];
    
    // Initialize all months with 0
    for (let i = 1; i <= 12; i++) {
        labels.push(months[i - 1]);
        issuedData.push(0);
        usedData.push(0);
    }
    
    // Fill in actual data
    monthlyTrends.forEach(trend => {
        const monthIndex = trend.month - 1;
        issuedData[monthIndex] = trend.credits_issued;
        usedData[monthIndex] = trend.credits_used;
    });
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Credits Issued',
                data: issuedData,
                borderColor: '@primaryColor()',
                backgroundColor: '@primaryColor()' + '20',
                tension: 0.4,
                fill: true
            }, {
                label: 'Credits Used',
                data: usedData,
                borderColor: '#dc3545',
                backgroundColor: '#dc354520',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
});
</script>
@endpush
