@extends('admin.layouts.app')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')

@section('content')
<div class="row">
    <!-- Statistics Cards -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, @primaryColor() 0%, #FFE55C 100%);">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas fa-building fa-3x text-dark"></i>
                    </div>
                    <div>
                        <h3 class="fw-bold text-dark mb-1">{{ $stats['total_properties'] }}</h3>
                        <p class="mb-0 text-dark">Total Properties</p>
                        <small class="text-muted">{{ $stats['active_properties'] }} active</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, @secondaryColor() 0%, #90EE90 100%);">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas fa-users fa-3x text-white"></i>
                    </div>
                    <div>
                        <h3 class="fw-bold text-white mb-1">{{ $stats['total_users'] }}</h3>
                        <p class="mb-0 text-white">Total Users</p>
                        <small class="text-light">Registered guests</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas fa-calendar-check fa-3x text-white"></i>
                    </div>
                    <div>
                        <h3 class="fw-bold text-white mb-1">{{ $stats['total_bookings'] }}</h3>
                        <p class="mb-0 text-white">Total Bookings</p>
                        <small class="text-light">{{ $stats['confirmed_bookings'] }} confirmed</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas fa-coins fa-3x text-white"></i>
                    </div>
                    <div>
                        <h3 class="fw-bold text-white mb-1">{{ $stats['total_credits_issued'] ?? 0 }}</h3>
                        <p class="mb-0 text-white">Credits Issued</p>
                        <small class="text-light">{{ $stats['total_credits_used'] ?? 0 }} used</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Analytics Section -->
<div class="row mt-4">
    <div class="col-lg-8 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="mb-0" style="color: @secondaryColor();">
                    <i class="fas fa-chart-line me-2"></i>Booking Trends (Last 30 Days)
                </h5>
            </div>
            <div class="card-body">
                <canvas id="bookingTrendsChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="mb-0" style="color: @secondaryColor();">
                    <i class="fas fa-chart-pie me-2"></i>Booking Status
                </h5>
            </div>
            <div class="card-body">
                <canvas id="bookingStatusChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="mb-0" style="color: @secondaryColor();">
                    <i class="fas fa-coins me-2"></i>Credit Usage Analytics
                </h5>
            </div>
            <div class="card-body">
                <canvas id="creditUsageChart" height="150"></canvas>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="mb-0" style="color: @secondaryColor();">
                    <i class="fas fa-map-marker-alt me-2"></i>Popular Locations
                </h5>
            </div>
            <div class="card-body">
                <canvas id="popularLocationsChart" height="150"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity Section -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="mb-0" style="color: @secondaryColor();">
                    <i class="fas fa-clock me-2"></i>Recent Bookings
                </h5>
            </div>
            <div class="card-body">
                @if($recent_bookings->count() > 0)
                    @foreach($recent_bookings as $booking)
                    <div class="d-flex align-items-center mb-3 pb-3 {{ !$loop->last ? 'border-bottom' : '' }}">
                        <div class="me-3">
                            <img src="{{ $booking->property->primary_image_url }}"
                                 class="rounded"
                                 width="50" height="50"
                                 style="object-fit: cover;"
                                 alt="{{ $booking->property->name }}">
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ $booking->property->name }}</h6>
                            <p class="mb-1 text-muted small">{{ $booking->user->name }}</p>
                            <small class="text-muted">{{ $booking->created_at->diffForHumans() }}</small>
                        </div>
                        <div>
                            <span class="badge bg-{{ $booking->status === 'confirmed' ? 'success' : ($booking->status === 'pending' ? 'warning' : 'secondary') }}">
                                {{ ucfirst($booking->status) }}
                            </span>
                        </div>
                    </div>
                    @endforeach
                @else
                    <p class="text-muted text-center py-4">No recent bookings</p>
                @endif
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="mb-0" style="color: @secondaryColor();">
                    <i class="fas fa-star me-2"></i>Recent Reviews
                </h5>
            </div>
            <div class="card-body">
                @if($recent_reviews->count() > 0)
                    @foreach($recent_reviews as $review)
                    <div class="d-flex align-items-start mb-3 pb-3 {{ !$loop->last ? 'border-bottom' : '' }}">
                        <div class="me-3">
                            <img src="{{ $review->user->avatar_url }}"
                                 class="rounded-circle"
                                 width="40" height="40"
                                 alt="{{ $review->user->name }}">
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ $review->property->name }}</h6>
                            <div class="mb-1">
                                @for($i = 1; $i <= 5; $i++)
                                    <i class="fas fa-star {{ $i <= $review->rating ? 'text-warning' : 'text-muted' }}"></i>
                                @endfor
                            </div>
                            <p class="mb-1 small">{{ Str::limit($review->comment, 80) }}</p>
                            <small class="text-muted">by {{ $review->user->name }} • {{ $review->created_at->diffForHumans() }}</small>
                        </div>
                    </div>
                    @endforeach
                @else
                    <p class="text-muted text-center py-4">No pending reviews</p>
                @endif
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Theme colors
    const primaryColor = '#FFD700';
    const secondaryColor = '#2E8B57';

    // Booking Trends Chart
    const bookingTrendsCtx = document.getElementById('bookingTrendsChart').getContext('2d');
    new Chart(bookingTrendsCtx, {
        type: 'line',
        data: {
            labels: {!! json_encode($chartData['booking_trends']['labels'] ?? []) !!},
            datasets: [{
                label: 'Bookings',
                data: {!! json_encode($chartData['booking_trends']['data'] ?? []) !!},
                borderColor: secondaryColor,
                backgroundColor: secondaryColor + '20',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });

    // Booking Status Chart
    const bookingStatusCtx = document.getElementById('bookingStatusChart').getContext('2d');
    new Chart(bookingStatusCtx, {
        type: 'doughnut',
        data: {
            labels: ['Confirmed', 'Pending', 'Cancelled'],
            datasets: [{
                data: [
                    {{ $stats['confirmed_bookings'] }},
                    {{ $stats['pending_bookings'] }},
                    {{ $stats['total_bookings'] - $stats['confirmed_bookings'] - $stats['pending_bookings'] }}
                ],
                backgroundColor: [
                    secondaryColor,
                    primaryColor,
                    '#dc3545'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Credit Usage Chart
    const creditUsageCtx = document.getElementById('creditUsageChart').getContext('2d');
    new Chart(creditUsageCtx, {
        type: 'bar',
        data: {
            labels: {!! json_encode($chartData['credit_usage']['labels'] ?? []) !!},
            datasets: [{
                label: 'Credits Used',
                data: {!! json_encode($chartData['credit_usage']['data'] ?? []) !!},
                backgroundColor: primaryColor,
                borderColor: primaryColor,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });

    // Popular Locations Chart
    const popularLocationsCtx = document.getElementById('popularLocationsChart').getContext('2d');
    new Chart(popularLocationsCtx, {
        type: 'bar',
        data: {
            labels: {!! json_encode($chartData['popular_locations']['labels'] ?? []) !!},
            datasets: [{
                label: 'Bookings',
                data: {!! json_encode($chartData['popular_locations']['data'] ?? []) !!},
                backgroundColor: [
                    primaryColor,
                    secondaryColor,
                    '#17a2b8',
                    '#6f42c1',
                    '#e83e8c'
                ]
            }]
        },
        options: {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
});
</script>
@endpush
