@extends('admin.layouts.app')

@section('title', 'Edit Booking - ' . $booking->booking_reference)
@section('page-title', 'Edit Booking')

@section('content')
<div class="row mb-4">
    <div class="col-md-8">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.bookings.index') }}">Bookings</a></li>
                <li class="breadcrumb-item"><a href="{{ route('admin.bookings.show', $booking) }}">{{ $booking->booking_reference }}</a></li>
                <li class="breadcrumb-item active">Edit</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="mb-0" style="color: @secondaryColor();">
                    <i class="fas fa-edit me-2"></i>Edit Booking - {{ $booking->booking_reference }}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.bookings.update', $booking) }}">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <!-- Booking Information -->
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">Booking Information</h6>
                            
                            <div class="mb-3">
                                <label class="form-label">Booking Reference</label>
                                <input type="text" class="form-control" value="{{ $booking->booking_reference }}" readonly>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Guest</label>
                                <div class="d-flex align-items-center p-2 bg-light rounded">
                                    <img src="{{ $booking->user->avatar_url }}" 
                                         class="rounded-circle me-2" 
                                         width="32" height="32" 
                                         alt="{{ $booking->user->name }}">
                                    <div>
                                        <div class="fw-medium">{{ $booking->user->name }}</div>
                                        <small class="text-muted">{{ $booking->user->email }}</small>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Property</label>
                                <div class="p-2 bg-light rounded">
                                    <div class="fw-medium">{{ $booking->property->title }}</div>
                                    <small class="text-muted">{{ $booking->property->location }}</small>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-6">
                                    <div class="mb-3">
                                        <label class="form-label">Check-in Date</label>
                                        <input type="text" class="form-control" 
                                               value="{{ $booking->check_in_date->format('M d, Y') }}" readonly>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="mb-3">
                                        <label class="form-label">Check-out Date</label>
                                        <input type="text" class="form-control" 
                                               value="{{ $booking->check_out_date->format('M d, Y') }}" readonly>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Duration</label>
                                <input type="text" class="form-control" 
                                       value="{{ $booking->check_in_date->diffInDays($booking->check_out_date) }} nights" readonly>
                            </div>
                        </div>

                        <!-- Editable Guest Details -->
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">Guest Details</h6>
                            
                            <div class="mb-3">
                                <label for="adults" class="form-label">Adults <span class="text-danger">*</span></label>
                                <select name="adults" id="adults" class="form-select @error('adults') is-invalid @enderror" required>
                                    @for($i = 1; $i <= 10; $i++)
                                        <option value="{{ $i }}" {{ old('adults', $booking->adults) == $i ? 'selected' : '' }}>
                                            {{ $i }} Adult{{ $i > 1 ? 's' : '' }}
                                        </option>
                                    @endfor
                                </select>
                                @error('adults')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="children" class="form-label">Children</label>
                                <select name="children" id="children" class="form-select @error('children') is-invalid @enderror">
                                    @for($i = 0; $i <= 10; $i++)
                                        <option value="{{ $i }}" {{ old('children', $booking->children) == $i ? 'selected' : '' }}>
                                            {{ $i }} {{ $i === 1 ? 'Child' : 'Children' }}
                                        </option>
                                    @endfor
                                </select>
                                @error('children')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Total Guests</label>
                                <input type="text" id="totalGuests" class="form-control" readonly>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Current Status</label>
                                <span class="badge bg-{{ $booking->status === 'confirmed' ? 'success' : ($booking->status === 'pending' ? 'warning' : 'danger') }} fs-6 ms-2">
                                    {{ ucfirst($booking->status) }}
                                </span>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Booking Date</label>
                                <input type="text" class="form-control" 
                                       value="{{ $booking->created_at->format('M d, Y H:i') }}" readonly>
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Special Requests -->
                    <div class="mb-3">
                        <label for="special_requests" class="form-label">Special Requests</label>
                        <textarea name="special_requests" id="special_requests" 
                                  class="form-control @error('special_requests') is-invalid @enderror" 
                                  rows="3" placeholder="Any special requests from the guest...">{{ old('special_requests', $booking->special_requests) }}</textarea>
                        @error('special_requests')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Admin Notes -->
                    <div class="mb-4">
                        <label for="admin_notes" class="form-label">Admin Notes</label>
                        <textarea name="admin_notes" id="admin_notes" 
                                  class="form-control @error('admin_notes') is-invalid @enderror" 
                                  rows="3" placeholder="Internal notes about this booking...">{{ old('admin_notes', $booking->admin_notes) }}</textarea>
                        @error('admin_notes')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">These notes are only visible to administrators.</div>
                    </div>

                    <!-- Pricing Information (Read-only) -->
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">Pricing Information</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-2">
                                        <span class="text-muted">Credits Used:</span>
                                        <span class="fw-bold ms-2">{{ $booking->check_in_date->diffInDays($booking->check_out_date) }}</span>
                                    </div>
                                    <div class="mb-2">
                                        <span class="text-muted">Base Price:</span>
                                        <span class="ms-2">${{ number_format($booking->total_price - $booking->booking_fee - $booking->cleaning_fee, 2) }}</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    @if($booking->booking_fee > 0)
                                    <div class="mb-2">
                                        <span class="text-muted">Booking Fee:</span>
                                        <span class="ms-2">${{ number_format($booking->booking_fee, 2) }}</span>
                                    </div>
                                    @endif
                                    @if($booking->cleaning_fee > 0)
                                    <div class="mb-2">
                                        <span class="text-muted">Cleaning Fee:</span>
                                        <span class="ms-2">${{ number_format($booking->cleaning_fee, 2) }}</span>
                                    </div>
                                    @endif
                                    <div class="mb-2 fw-bold">
                                        <span class="text-muted">Total:</span>
                                        <span class="ms-2">${{ number_format($booking->total_price, 2) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.bookings.show', $booking) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Booking
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Update Booking
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const adultsSelect = document.getElementById('adults');
    const childrenSelect = document.getElementById('children');
    const totalGuestsInput = document.getElementById('totalGuests');

    function updateTotalGuests() {
        const adults = parseInt(adultsSelect.value) || 0;
        const children = parseInt(childrenSelect.value) || 0;
        const total = adults + children;
        totalGuestsInput.value = `${total} Guest${total !== 1 ? 's' : ''}`;
    }

    adultsSelect.addEventListener('change', updateTotalGuests);
    childrenSelect.addEventListener('change', updateTotalGuests);

    // Initialize total guests
    updateTotalGuests();
});
</script>
@endpush
