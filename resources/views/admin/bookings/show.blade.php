@extends('admin.layouts.app')

@section('title', 'Booking Details - ' . $booking->booking_reference)
@section('page-title', 'Booking Details')

@section('content')
<div class="row mb-4">
    <div class="col-md-8">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.bookings.index') }}">Bookings</a></li>
                <li class="breadcrumb-item active">{{ $booking->booking_reference }}</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ route('admin.bookings.edit', $booking) }}" class="btn btn-primary me-2">
            <i class="fas fa-edit me-1"></i>Edit Booking
        </a>
        <div class="btn-group">
            <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-cog me-1"></i>Status
            </button>
            <ul class="dropdown-menu">
                @if($booking->status !== 'confirmed')
                <li><a class="dropdown-item" href="#" onclick="updateStatus('confirmed')">
                    <i class="fas fa-check text-success me-2"></i>Confirm Booking
                </a></li>
                @endif
                @if($booking->status !== 'pending')
                <li><a class="dropdown-item" href="#" onclick="updateStatus('pending')">
                    <i class="fas fa-clock text-warning me-2"></i>Mark as Pending
                </a></li>
                @endif
                @if($booking->status !== 'cancelled')
                <li><a class="dropdown-item" href="#" onclick="updateStatus('cancelled')">
                    <i class="fas fa-times text-danger me-2"></i>Cancel Booking
                </a></li>
                @endif
            </ul>
        </div>
    </div>
</div>

<div class="row">
    <!-- Booking Information -->
    <div class="col-lg-8 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0" style="color: @secondaryColor();">
                        <i class="fas fa-calendar-check me-2"></i>Booking Information
                    </h5>
                    <span class="badge bg-{{ $booking->status === 'confirmed' ? 'success' : ($booking->status === 'pending' ? 'warning' : 'danger') }} fs-6">
                        {{ ucfirst($booking->status) }}
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Booking Details</h6>
                        <div class="mb-3">
                            <label class="form-label text-muted">Booking Reference</label>
                            <div class="fw-bold">{{ $booking->booking_reference }}</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Check-in Date</label>
                            <div>{{ $booking->check_in_date->format('F d, Y') }}</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Check-out Date</label>
                            <div>{{ $booking->check_out_date->format('F d, Y') }}</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Duration</label>
                            <div>{{ $booking->check_in_date->diffInDays($booking->check_out_date) }} nights</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Guest Information</h6>
                        <div class="mb-3">
                            <label class="form-label text-muted">Adults</label>
                            <div>{{ $booking->adults }}</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Children</label>
                            <div>{{ $booking->children }}</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Total Guests</label>
                            <div>{{ $booking->adults + $booking->children }}</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Booking Date</label>
                            <div>{{ $booking->created_at->format('F d, Y H:i') }}</div>
                        </div>
                    </div>
                </div>

                @if($booking->special_requests)
                <hr>
                <div class="mb-3">
                    <label class="form-label text-muted">Special Requests</label>
                    <div class="bg-light p-3 rounded">{{ $booking->special_requests }}</div>
                </div>
                @endif

                @if($booking->admin_notes)
                <div class="mb-0">
                    <label class="form-label text-muted">Admin Notes</label>
                    <div class="bg-warning bg-opacity-10 p-3 rounded border border-warning border-opacity-25">
                        {{ $booking->admin_notes }}
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Property Information -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="mb-0" style="color: @secondaryColor();">
                    <i class="fas fa-building me-2"></i>Property Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        @if($booking->property->images->count() > 0)
                            <img src="{{ asset('storage/' . $booking->property->images->first()->image_path) }}" 
                                 class="img-fluid rounded" 
                                 alt="{{ $booking->property->title }}">
                        @else
                            <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 200px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        @endif
                    </div>
                    <div class="col-md-8">
                        <h4 class="mb-3">{{ $booking->property->title }}</h4>
                        <div class="mb-2">
                            <i class="fas fa-map-marker-alt text-muted me-2"></i>
                            {{ $booking->property->location }}
                        </div>
                        <div class="mb-2">
                            <i class="fas fa-users text-muted me-2"></i>
                            Up to {{ $booking->property->max_guests }} guests
                        </div>
                        <div class="mb-2">
                            <i class="fas fa-bed text-muted me-2"></i>
                            {{ $booking->property->bedrooms }} bedroom{{ $booking->property->bedrooms > 1 ? 's' : '' }}
                        </div>
                        <div class="mb-3">
                            <i class="fas fa-bath text-muted me-2"></i>
                            {{ $booking->property->bathrooms }} bathroom{{ $booking->property->bathrooms > 1 ? 's' : '' }}
                        </div>
                        <a href="{{ route('admin.properties.show', $booking->property) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-external-link-alt me-1"></i>View Property
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Guest Profile -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-0 py-3">
                <h6 class="mb-0">Guest Profile</h6>
            </div>
            <div class="card-body text-center">
                <img src="{{ $booking->user->avatar_url }}" 
                     class="rounded-circle mb-3" 
                     width="80" height="80" 
                     alt="{{ $booking->user->name }}">
                <h5 class="mb-1">{{ $booking->user->name }}</h5>
                <p class="text-muted mb-3">{{ $booking->user->email }}</p>
                
                <div class="row text-center mb-3">
                    <div class="col-6">
                        <div class="border-end">
                            <h6 class="mb-0" style="color: @primaryColor();">{{ $booking->user->credits }}</h6>
                            <small class="text-muted">Credits</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h6 class="mb-0" style="color: @secondaryColor();">{{ $booking->user->bookings->count() }}</h6>
                        <small class="text-muted">Bookings</small>
                    </div>
                </div>

                <a href="{{ route('admin.users.show', $booking->user) }}" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-user me-1"></i>View Profile
                </a>
            </div>
        </div>

        <!-- Pricing Information -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-0 py-3">
                <h6 class="mb-0">Pricing Details</h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>Credits Used:</span>
                    <span class="fw-bold">{{ $booking->check_in_date->diffInDays($booking->check_out_date) }}</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>Base Price:</span>
                    <span>${{ number_format($booking->total_price - $booking->booking_fee - $booking->cleaning_fee, 2) }}</span>
                </div>
                @if($booking->booking_fee > 0)
                <div class="d-flex justify-content-between mb-2">
                    <span>Booking Fee:</span>
                    <span>${{ number_format($booking->booking_fee, 2) }}</span>
                </div>
                @endif
                @if($booking->cleaning_fee > 0)
                <div class="d-flex justify-content-between mb-2">
                    <span>Cleaning Fee:</span>
                    <span>${{ number_format($booking->cleaning_fee, 2) }}</span>
                </div>
                @endif
                <hr>
                <div class="d-flex justify-content-between fw-bold">
                    <span>Total:</span>
                    <span>${{ number_format($booking->total_price, 2) }}</span>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <h6 class="mb-0">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#notesModal">
                        <i class="fas fa-sticky-note me-2"></i>Add Admin Notes
                    </button>
                    <a href="mailto:{{ $booking->user->email }}" class="btn btn-outline-secondary">
                        <i class="fas fa-envelope me-2"></i>Email Guest
                    </a>
                    @if($booking->user->phone)
                    <a href="tel:{{ $booking->user->phone }}" class="btn btn-outline-success">
                        <i class="fas fa-phone me-2"></i>Call Guest
                    </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Form (Hidden) -->
<form id="statusUpdateForm" method="POST" action="{{ route('admin.bookings.update-status', $booking) }}" style="display: none;">
    @csrf
    @method('PATCH')
    <input type="hidden" name="status" id="statusInput">
</form>

<!-- Admin Notes Modal -->
<div class="modal fade" id="notesModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Admin Notes</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('admin.bookings.update-status', $booking) }}">
                @csrf
                @method('PATCH')
                <input type="hidden" name="status" value="{{ $booking->status }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Current Notes</label>
                        <div class="bg-light p-3 rounded">
                            {{ $booking->admin_notes ?: 'No notes added yet.' }}
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Admin Notes</label>
                        <textarea name="admin_notes" class="form-control" rows="4" 
                                  placeholder="Add notes about this booking...">{{ $booking->admin_notes }}</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Notes</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function updateStatus(status) {
    if (confirm(`Are you sure you want to ${status} this booking?`)) {
        document.getElementById('statusInput').value = status;
        document.getElementById('statusUpdateForm').submit();
    }
}
</script>
@endpush
