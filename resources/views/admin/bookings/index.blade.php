@extends('admin.layouts.app')

@section('title', 'Booking Management')
@section('page-title', 'Booking Management')

@section('content')
<div class="row mb-4">
    <div class="col-md-8">
        <h4 class="mb-0" style="color: @secondaryColor();">
            <i class="fas fa-calendar-check me-2"></i>Manage Bookings
        </h4>
        <p class="text-muted">Manage all property bookings and reservations</p>
    </div>
    <div class="col-md-4 text-end">
        <button type="button" class="btn btn-info me-2" data-bs-toggle="modal" data-bs-target="#analyticsModal">
            <i class="fas fa-chart-bar me-1"></i>Analytics
        </button>
        <a href="{{ route('admin.bookings.export', request()->query()) }}" class="btn btn-outline-secondary">
            <i class="fas fa-download me-1"></i>Export
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-2 mb-3">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, @primaryColor() 0%, #FFE55C 100%);">
            <div class="card-body text-center p-3">
                <i class="fas fa-calendar-check fa-2x text-dark mb-2"></i>
                <h5 class="fw-bold text-dark mb-0">{{ $stats['total_bookings'] }}</h5>
                <small class="text-dark">Total</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #ffc107 0%, #fff3cd 100%);">
            <div class="card-body text-center p-3">
                <i class="fas fa-clock fa-2x text-dark mb-2"></i>
                <h5 class="fw-bold text-dark mb-0">{{ $stats['pending_bookings'] }}</h5>
                <small class="text-dark">Pending</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, @secondaryColor() 0%, #90EE90 100%);">
            <div class="card-body text-center p-3">
                <i class="fas fa-check-circle fa-2x text-white mb-2"></i>
                <h5 class="fw-bold text-white mb-0">{{ $stats['confirmed_bookings'] }}</h5>
                <small class="text-white">Confirmed</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #dc3545 0%, #f8d7da 100%);">
            <div class="card-body text-center p-3">
                <i class="fas fa-times-circle fa-2x text-white mb-2"></i>
                <h5 class="fw-bold text-white mb-0">{{ $stats['cancelled_bookings'] }}</h5>
                <small class="text-white">Cancelled</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #17a2b8 0%, #7dd3fc 100%);">
            <div class="card-body text-center p-3">
                <i class="fas fa-dollar-sign fa-2x text-white mb-2"></i>
                <h5 class="fw-bold text-white mb-0">${{ number_format($stats['total_revenue'], 0) }}</h5>
                <small class="text-white">Revenue</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #6f42c1 0%, #a855f7 100%);">
            <div class="card-body text-center p-3">
                <i class="fas fa-calendar-alt fa-2x text-white mb-2"></i>
                <h5 class="fw-bold text-white mb-0">{{ $stats['this_month_bookings'] }}</h5>
                <small class="text-white">This Month</small>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.bookings.index') }}" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Search</label>
                <input type="text" name="search" class="form-control" 
                       placeholder="Reference, user, or property..." 
                       value="{{ request('search') }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">Status</label>
                <select name="status" class="form-select">
                    <option value="">All Statuses</option>
                    <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                    <option value="confirmed" {{ request('status') === 'confirmed' ? 'selected' : '' }}>Confirmed</option>
                    <option value="cancelled" {{ request('status') === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Property</label>
                <select name="property_id" class="form-select">
                    <option value="">All Properties</option>
                    @foreach($properties as $property)
                        <option value="{{ $property->id }}" {{ request('property_id') == $property->id ? 'selected' : '' }}>
                            {{ Str::limit($property->title, 30) }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">From Date</label>
                <input type="date" name="date_from" class="form-control" 
                       value="{{ request('date_from') }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">To Date</label>
                <input type="date" name="date_to" class="form-control" 
                       value="{{ request('date_to') }}">
            </div>
            <div class="col-md-1 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Bookings Table -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-0 py-3">
        <div class="d-flex justify-content-between align-items-center">
            <h6 class="mb-0">Bookings ({{ $bookings->total() }})</h6>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleBulkActions()">
                    <i class="fas fa-tasks me-1"></i>Bulk Actions
                </button>
                <select class="form-select form-select-sm" onchange="updateSort(this)" style="width: auto;">
                    <option value="created_at-desc" {{ request('sort_by') == 'created_at' && request('sort_order') == 'desc' ? 'selected' : '' }}>Newest First</option>
                    <option value="created_at-asc" {{ request('sort_by') == 'created_at' && request('sort_order') == 'asc' ? 'selected' : '' }}>Oldest First</option>
                    <option value="check_in_date-asc" {{ request('sort_by') == 'check_in_date' && request('sort_order') == 'asc' ? 'selected' : '' }}>Check-in Date</option>
                    <option value="total_price-desc" {{ request('sort_by') == 'total_price' && request('sort_order') == 'desc' ? 'selected' : '' }}>Highest Price</option>
                </select>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        @if($bookings->count() > 0)
            <!-- Bulk Actions Bar (Hidden by default) -->
            <div id="bulkActionsBar" class="bg-light p-3 border-bottom" style="display: none;">
                <form method="POST" action="{{ route('admin.bookings.bulk-update') }}" id="bulkForm">
                    @csrf
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <span id="selectedCount">0</span> bookings selected
                        </div>
                        <div class="col-md-6 text-end">
                            <select name="action" class="form-select form-select-sm d-inline-block w-auto me-2" required>
                                <option value="">Select Action</option>
                                <option value="confirm">Confirm Selected</option>
                                <option value="cancel">Cancel Selected</option>
                                <option value="pending">Mark as Pending</option>
                            </select>
                            <button type="submit" class="btn btn-sm btn-primary">Apply</button>
                            <button type="button" class="btn btn-sm btn-secondary" onclick="toggleBulkActions()">Cancel</button>
                        </div>
                    </div>
                </form>
            </div>

            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="40">
                                <input type="checkbox" id="selectAll" class="form-check-input" style="display: none;">
                            </th>
                            <th>Booking Details</th>
                            <th>Guest</th>
                            <th>Property</th>
                            <th>Dates</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($bookings as $booking)
                        <tr>
                            <td>
                                <input type="checkbox" name="booking_ids[]" value="{{ $booking->id }}" 
                                       class="form-check-input booking-checkbox" style="display: none;">
                            </td>
                            <td>
                                <div>
                                    <div class="fw-bold">{{ $booking->booking_reference }}</div>
                                    <small class="text-muted">
                                        {{ $booking->adults }} adult{{ $booking->adults > 1 ? 's' : '' }}
                                        @if($booking->children > 0), {{ $booking->children }} child{{ $booking->children > 1 ? 'ren' : '' }}@endif
                                    </small>
                                    <div class="small text-muted">{{ $booking->created_at->format('M d, Y H:i') }}</div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <img src="{{ $booking->user->avatar_url }}" 
                                         class="rounded-circle me-2" 
                                         width="32" height="32" 
                                         alt="{{ $booking->user->name }}">
                                    <div>
                                        <div class="fw-medium">{{ $booking->user->name }}</div>
                                        <small class="text-muted">{{ $booking->user->email }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <div class="fw-medium">{{ Str::limit($booking->property->title, 30) }}</div>
                                    <small class="text-muted">{{ $booking->property->location }}</small>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <div class="fw-medium">{{ $booking->check_in_date->format('M d') }} - {{ $booking->check_out_date->format('M d, Y') }}</div>
                                    <small class="text-muted">{{ $booking->check_in_date->diffInDays($booking->check_out_date) }} nights</small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{{ $booking->status === 'confirmed' ? 'success' : ($booking->status === 'pending' ? 'warning' : 'danger') }} fs-6">
                                    {{ ucfirst($booking->status) }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.bookings.show', $booking) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.bookings.edit', $booking) }}" 
                                       class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-success dropdown-toggle" 
                                                data-bs-toggle="dropdown">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            @if($booking->status !== 'confirmed')
                                            <li><a class="dropdown-item" href="#" onclick="updateStatus({{ $booking->id }}, 'confirmed')">
                                                <i class="fas fa-check text-success me-2"></i>Confirm
                                            </a></li>
                                            @endif
                                            @if($booking->status !== 'pending')
                                            <li><a class="dropdown-item" href="#" onclick="updateStatus({{ $booking->id }}, 'pending')">
                                                <i class="fas fa-clock text-warning me-2"></i>Mark Pending
                                            </a></li>
                                            @endif
                                            @if($booking->status !== 'cancelled')
                                            <li><a class="dropdown-item" href="#" onclick="updateStatus({{ $booking->id }}, 'cancelled')">
                                                <i class="fas fa-times text-danger me-2"></i>Cancel
                                            </a></li>
                                            @endif
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="card-footer bg-white border-0">
                {{ $bookings->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No bookings found</h5>
                <p class="text-muted">No bookings match your current filters.</p>
            </div>
        @endif
    </div>
</div>

<!-- Status Update Form (Hidden) -->
<form id="statusUpdateForm" method="POST" style="display: none;">
    @csrf
    @method('PATCH')
    <input type="hidden" name="status" id="statusInput">
</form>
@endsection

@push('scripts')
<script>
function updateSort(select) {
    const [sortBy, sortOrder] = select.value.split('-');
    const url = new URL(window.location);
    url.searchParams.set('sort_by', sortBy);
    url.searchParams.set('sort_order', sortOrder);
    window.location = url;
}

function toggleBulkActions() {
    const bulkBar = document.getElementById('bulkActionsBar');
    const checkboxes = document.querySelectorAll('.booking-checkbox, #selectAll');
    
    if (bulkBar.style.display === 'none') {
        bulkBar.style.display = 'block';
        checkboxes.forEach(cb => cb.style.display = 'inline-block');
    } else {
        bulkBar.style.display = 'none';
        checkboxes.forEach(cb => {
            cb.style.display = 'none';
            cb.checked = false;
        });
        updateSelectedCount();
    }
}

function updateStatus(bookingId, status) {
    if (confirm(`Are you sure you want to ${status} this booking?`)) {
        const form = document.getElementById('statusUpdateForm');
        form.action = `/admin/bookings/${bookingId}/status`;
        document.getElementById('statusInput').value = status;
        form.submit();
    }
}

// Bulk selection handling
document.addEventListener('DOMContentLoaded', function() {
    const selectAll = document.getElementById('selectAll');
    const bookingCheckboxes = document.querySelectorAll('.booking-checkbox');
    const bulkForm = document.getElementById('bulkForm');

    selectAll.addEventListener('change', function() {
        bookingCheckboxes.forEach(cb => cb.checked = this.checked);
        updateSelectedCount();
    });

    bookingCheckboxes.forEach(cb => {
        cb.addEventListener('change', updateSelectedCount);
    });

    bulkForm.addEventListener('submit', function(e) {
        const selected = document.querySelectorAll('.booking-checkbox:checked');
        if (selected.length === 0) {
            e.preventDefault();
            alert('Please select at least one booking.');
            return;
        }

        // Add selected booking IDs to form
        selected.forEach(cb => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'booking_ids[]';
            input.value = cb.value;
            this.appendChild(input);
        });
    });

    function updateSelectedCount() {
        const selected = document.querySelectorAll('.booking-checkbox:checked');
        document.getElementById('selectedCount').textContent = selected.length;
        
        selectAll.checked = selected.length === bookingCheckboxes.length;
        selectAll.indeterminate = selected.length > 0 && selected.length < bookingCheckboxes.length;
    }
});
</script>
@endpush
