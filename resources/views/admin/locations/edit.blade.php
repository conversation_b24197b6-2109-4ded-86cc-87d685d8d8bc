@extends('admin.layouts.app')

@section('title', 'Edit Location')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Edit Location</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.locations.index') }}">Locations</a></li>
                    <li class="breadcrumb-item active">Edit {{ $location->name }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.locations.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Locations
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom py-3">
                    <h5 class="mb-0">Location Information</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.locations.update', $location) }}" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="name" class="form-label">Location Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name', $location->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Enter a descriptive name for this location</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="country" class="form-label">Country <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('country') is-invalid @enderror" 
                                       id="country" name="country" value="{{ old('country', $location->country) }}" required>
                                @error('country')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="state" class="form-label">State/Province</label>
                                <input type="text" class="form-control @error('state') is-invalid @enderror" 
                                       id="state" name="state" value="{{ old('state', $location->state) }}">
                                @error('state')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="city" class="form-label">City</label>
                                <input type="text" class="form-control @error('city') is-invalid @enderror" 
                                       id="city" name="city" value="{{ old('city', $location->city) }}">
                                @error('city')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="image" class="form-label">Location Image</label>
                            @if($location->image_url)
                                <div class="mb-2">
                                    <img src="{{ asset('storage/' . $location->image_url) }}" 
                                         class="img-thumbnail" style="max-height: 150px;" alt="{{ $location->name }}">
                                    <div class="form-text">Current image</div>
                                </div>
                            @endif
                            <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                   id="image" name="image" accept="image/*">
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Upload a new image to replace the current one. Max size: 2MB. Formats: JPEG, PNG, JPG, GIF</div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Update Location
                            </button>
                            <a href="{{ route('admin.locations.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom py-3">
                    <h5 class="mb-0">Location Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <div class="h4 mb-0" style="color: var(--primary-color);">{{ $location->properties()->count() }}</div>
                                <div class="small text-muted">Properties</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="h4 mb-0" style="color: var(--accent-color);">{{ $location->properties()->where('status', 'active')->count() }}</div>
                            <div class="small text-muted">Active</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-bottom py-3">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.locations.show', $location) }}" class="btn btn-outline-primary">
                            <i class="fas fa-eye me-1"></i>View Details
                        </a>
                        @if($location->properties()->count() > 0)
                            <a href="{{ route('admin.properties.index', ['location_id' => $location->id]) }}" class="btn btn-outline-success">
                                <i class="fas fa-building me-1"></i>View Properties
                            </a>
                        @endif
                    </div>
                </div>
            </div>

            @if($location->properties()->count() == 0)
                <div class="card border-0 shadow-sm mt-4 border-danger">
                    <div class="card-header bg-danger bg-opacity-10 border-bottom py-3">
                        <h5 class="mb-0 text-danger">Danger Zone</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted small mb-3">This location has no properties and can be safely deleted.</p>
                        <form method="POST" action="{{ route('admin.locations.destroy', $location) }}" 
                              onsubmit="return confirm('Are you sure you want to delete this location? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="fas fa-trash me-1"></i>Delete Location
                            </button>
                        </form>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const imageInput = document.getElementById('image');
    
    // Handle image preview
    imageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // Create preview element if it doesn't exist
                let preview = document.getElementById('imagePreview');
                if (!preview) {
                    preview = document.createElement('div');
                    preview.id = 'imagePreview';
                    preview.className = 'mt-2';
                    imageInput.parentNode.insertBefore(preview, imageInput.nextSibling);
                }
                
                preview.innerHTML = `
                    <img src="${e.target.result}" class="img-thumbnail" style="max-height: 150px;" alt="Preview">
                    <div class="form-text">New image preview</div>
                `;
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>
@endpush
