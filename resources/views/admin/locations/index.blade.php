@extends('admin.layouts.app')

@section('title', 'Locations Management')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Locations Management</h1>
            <p class="text-muted">Manage property locations and destinations</p>
        </div>
        <div>
            <a href="{{ route('admin.locations.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>Add New Location
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: linear-gradient(135deg, var(--primary-color), var(--accent-color));">
                                <i class="fas fa-map-marker-alt text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold h4 mb-0">{{ number_format($stats['total_locations']) }}</div>
                            <div class="text-muted small">Total Locations</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3 bg-success bg-opacity-10">
                                <i class="fas fa-building text-success"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold h4 mb-0">{{ number_format($stats['total_properties']) }}</div>
                            <div class="text-muted small">Total Properties</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3 bg-info bg-opacity-10">
                                <i class="fas fa-globe text-info"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold h4 mb-0">{{ number_format($stats['countries_count']) }}</div>
                            <div class="text-muted small">Countries</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3 bg-warning bg-opacity-10">
                                <i class="fas fa-city text-warning"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold h4 mb-0">{{ number_format($stats['cities_count']) }}</div>
                            <div class="text-muted small">Cities</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.locations.index') }}" class="row g-3">
                <div class="col-md-6">
                    <label class="form-label">Search</label>
                    <input type="text" class="form-control" name="search" value="{{ request('search') }}" 
                           placeholder="Search locations, cities, countries...">
                </div>
                <div class="col-md-4">
                    <label class="form-label">Country</label>
                    <select class="form-select" name="country">
                        <option value="">All Countries</option>
                        @foreach($countries as $country)
                            <option value="{{ $country }}" {{ request('country') === $country ? 'selected' : '' }}>
                                {{ $country }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>Filter
                        </button>
                        <a href="{{ route('admin.locations.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Locations Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-bottom-0 py-3">
            <h5 class="mb-0">Locations ({{ $locations->total() }})</h5>
        </div>
        <div class="card-body p-0">
            @if($locations->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Location</th>
                                <th>Country</th>
                                <th>State/Province</th>
                                <th>City</th>
                                <th>Properties</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($locations as $location)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($location->image_url)
                                                <img src="{{ asset('storage/' . $location->image_url) }}" 
                                                     class="rounded me-3" width="50" height="40" 
                                                     style="object-fit: cover;" alt="{{ $location->name }}">
                                            @else
                                                <div class="rounded me-3 bg-light d-flex align-items-center justify-content-center" 
                                                     style="width: 50px; height: 40px;">
                                                    <i class="fas fa-map-marker-alt text-muted"></i>
                                                </div>
                                            @endif
                                            <div>
                                                <div class="fw-semibold">{{ $location->name }}</div>
                                                <div class="text-muted small">{{ $location->slug }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ $location->country }}</span>
                                    </td>
                                    <td>{{ $location->state ?: '-' }}</td>
                                    <td>{{ $location->city ?: '-' }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="fw-semibold">{{ $location->properties_count }}</span>
                                            @if($location->properties_count > 0)
                                                <a href="{{ route('admin.locations.show', $location) }}" 
                                                   class="btn btn-sm btn-outline-primary ms-2">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <a href="{{ route('admin.locations.show', $location) }}" 
                                               class="btn btn-sm btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.locations.edit', $location) }}" 
                                               class="btn btn-sm btn-outline-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @if($location->properties_count == 0)
                                                <form method="POST" action="{{ route('admin.locations.destroy', $location) }}" 
                                                      class="d-inline" onsubmit="return confirm('Are you sure you want to delete this location?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            @else
                                                <button class="btn btn-sm btn-outline-danger" disabled title="Cannot delete location with properties">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No locations found</h5>
                    <p class="text-muted">No locations match your current filters.</p>
                    <a href="{{ route('admin.locations.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Add First Location
                    </a>
                </div>
            @endif
        </div>
        @if($locations->hasPages())
            <div class="card-footer bg-white border-top-0">
                {{ $locations->links() }}
            </div>
        @endif
    </div>
</div>
@endsection
