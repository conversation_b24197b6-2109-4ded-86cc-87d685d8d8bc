@extends('admin.layouts.app')

@section('title', 'Location Details')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ $location->name }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.locations.index') }}">Locations</a></li>
                    <li class="breadcrumb-item active">{{ $location->name }}</li>
                </ol>
            </nav>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.locations.edit', $location) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i>Edit Location
            </a>
            <a href="{{ route('admin.locations.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Locations
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Location Information -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h4 class="mb-3">{{ $location->name }}</h4>
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-map-marker-alt me-2 text-muted"></i>
                                    <span>{{ $location->full_name }}</span>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-link me-2 text-muted"></i>
                                    <code>{{ $location->slug }}</code>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="mb-2">
                                        <strong>Country:</strong> {{ $location->country }}
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="mb-2">
                                        <strong>State/Province:</strong> {{ $location->state ?: 'Not specified' }}
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="mb-2">
                                        <strong>City:</strong> {{ $location->city ?: 'Not specified' }}
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="mb-2">
                                        <strong>Created:</strong> {{ $location->created_at->format('M d, Y') }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            @if($location->image_url)
                                <img src="{{ asset('storage/' . $location->image_url) }}" 
                                     class="img-fluid rounded" alt="{{ $location->name }}">
                            @else
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 200px;">
                                    <div class="text-center text-muted">
                                        <i class="fas fa-image fa-3x mb-2"></i>
                                        <div>No image available</div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Properties in this Location -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Properties in {{ $location->name }}</h5>
                        <span class="badge bg-primary">{{ $location->properties->count() }} Properties</span>
                    </div>
                </div>
                <div class="card-body">
                    @if($location->properties->count() > 0)
                        <div class="row">
                            @foreach($location->properties as $property)
                                <div class="col-md-6 mb-4">
                                    <div class="card h-100">
                                        <div class="row g-0 h-100">
                                            <div class="col-4">
                                                @if($property->images->count() > 0)
                                                    <img src="{{ asset('storage/' . $property->images->first()->image_path) }}" 
                                                         class="img-fluid rounded-start h-100" style="object-fit: cover;" 
                                                         alt="{{ $property->name }}">
                                                @else
                                                    <div class="bg-light rounded-start h-100 d-flex align-items-center justify-content-center">
                                                        <i class="fas fa-building text-muted"></i>
                                                    </div>
                                                @endif
                                            </div>
                                            <div class="col-8">
                                                <div class="card-body p-3 d-flex flex-column h-100">
                                                    <h6 class="card-title mb-2">{{ Str::limit($property->name, 30) }}</h6>
                                                    <div class="small text-muted mb-2">
                                                        <div class="d-flex align-items-center mb-1">
                                                            <i class="fas fa-coins me-1"></i>
                                                            {{ $property->credits_per_night }} credit{{ $property->credits_per_night > 1 ? 's' : '' }}/night
                                                        </div>
                                                        <div class="d-flex align-items-center mb-1">
                                                            <i class="fas fa-star me-1"></i>
                                                            {{ number_format($property->average_rating, 1) }} ({{ $property->reviews_count }} reviews)
                                                        </div>
                                                    </div>
                                                    <div class="mt-auto">
                                                        <span class="badge bg-{{ $property->status === 'active' ? 'success' : 'warning' }} mb-2">
                                                            {{ ucfirst($property->status) }}
                                                        </span>
                                                        <div class="d-flex gap-1">
                                                            <a href="{{ route('admin.properties.show', $property) }}" 
                                                               class="btn btn-sm btn-outline-primary">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <a href="{{ route('admin.properties.edit', $property) }}" 
                                                               class="btn btn-sm btn-outline-warning">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Properties</h5>
                            <p class="text-muted">This location doesn't have any properties yet.</p>
                            <a href="{{ route('admin.properties.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>Add First Property
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Analytics Sidebar -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom py-3">
                    <h5 class="mb-0">Analytics</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border-end">
                                <div class="h4 mb-0" style="color: var(--primary-color);">{{ $analytics['total_properties'] }}</div>
                                <div class="small text-muted">Total Properties</div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="h4 mb-0" style="color: var(--accent-color);">{{ $analytics['active_properties'] }}</div>
                            <div class="small text-muted">Active Properties</div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border-end">
                                <div class="h4 mb-0 text-info">{{ number_format($analytics['total_bookings']) }}</div>
                                <div class="small text-muted">Total Bookings</div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="h4 mb-0 text-warning">{{ number_format($analytics['total_reviews']) }}</div>
                            <div class="small text-muted">Total Reviews</div>
                        </div>
                    </div>
                    
                    @if($analytics['average_rating'] > 0)
                        <hr>
                        <div class="text-center">
                            <div class="h4 mb-1">{{ number_format($analytics['average_rating'], 1) }}</div>
                            <div class="d-flex justify-content-center mb-2">
                                @for($i = 1; $i <= 5; $i++)
                                    <i class="fas fa-star {{ $i <= $analytics['average_rating'] ? 'text-warning' : 'text-muted' }}"></i>
                                @endfor
                            </div>
                            <div class="small text-muted">Average Rating</div>
                        </div>
                    @endif
                </div>
            </div>

            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom py-3">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.locations.edit', $location) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-1"></i>Edit Location
                        </a>
                        @if($location->properties->count() > 0)
                            <a href="{{ route('admin.properties.index', ['location_id' => $location->id]) }}" class="btn btn-outline-primary">
                                <i class="fas fa-building me-1"></i>View All Properties
                            </a>
                        @else
                            <a href="{{ route('admin.properties.create') }}" class="btn btn-outline-success">
                                <i class="fas fa-plus me-1"></i>Add Property
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
