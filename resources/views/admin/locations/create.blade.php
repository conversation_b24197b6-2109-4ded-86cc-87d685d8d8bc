@extends('admin.layouts.app')

@section('title', 'Create Location')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Create New Location</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.locations.index') }}">Locations</a></li>
                    <li class="breadcrumb-item active">Create</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.locations.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Locations
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom py-3">
                    <h5 class="mb-0">Location Information</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.locations.store') }}" enctype="multipart/form-data">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="name" class="form-label">Location Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Enter a descriptive name for this location (e.g., "Bali Beach Resort Area")</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="country" class="form-label">Country <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('country') is-invalid @enderror" 
                                       id="country" name="country" value="{{ old('country') }}" required>
                                @error('country')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="state" class="form-label">State/Province</label>
                                <input type="text" class="form-control @error('state') is-invalid @enderror" 
                                       id="state" name="state" value="{{ old('state') }}">
                                @error('state')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="city" class="form-label">City</label>
                                <input type="text" class="form-control @error('city') is-invalid @enderror" 
                                       id="city" name="city" value="{{ old('city') }}">
                                @error('city')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="image" class="form-label">Location Image</label>
                            <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                   id="image" name="image" accept="image/*">
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Upload an image that represents this location. Max size: 2MB. Formats: JPEG, PNG, JPG, GIF</div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Create Location
                            </button>
                            <a href="{{ route('admin.locations.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom py-3">
                    <h5 class="mb-0">Tips</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="fas fa-lightbulb me-1"></i>Location Guidelines
                        </h6>
                        <ul class="mb-0 small">
                            <li>Use descriptive names that help guests identify the area</li>
                            <li>Include nearby landmarks or popular attractions in the name</li>
                            <li>Be consistent with naming conventions</li>
                            <li>Upload high-quality images that showcase the location</li>
                            <li>Fill in all location details for better organization</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-1"></i>Important Notes
                        </h6>
                        <ul class="mb-0 small">
                            <li>Location names must be unique</li>
                            <li>Once created, locations with properties cannot be deleted</li>
                            <li>Images should be landscape orientation for best display</li>
                            <li>Country field is required for proper categorization</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Preview Card -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-bottom py-3">
                    <h5 class="mb-0">Preview</h5>
                </div>
                <div class="card-body">
                    <div id="locationPreview" class="text-center text-muted">
                        <i class="fas fa-map-marker-alt fa-3x mb-3"></i>
                        <p>Fill in the form to see a preview</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const countryInput = document.getElementById('country');
    const stateInput = document.getElementById('state');
    const cityInput = document.getElementById('city');
    const imageInput = document.getElementById('image');
    const preview = document.getElementById('locationPreview');

    function updatePreview() {
        const name = nameInput.value.trim();
        const country = countryInput.value.trim();
        const state = stateInput.value.trim();
        const city = cityInput.value.trim();

        if (name || country || state || city) {
            let locationParts = [];
            if (city) locationParts.push(city);
            if (state) locationParts.push(state);
            if (country) locationParts.push(country);

            const fullLocation = locationParts.join(', ');

            preview.innerHTML = `
                <div class="text-start">
                    <h6 class="mb-2">${name || 'Location Name'}</h6>
                    <div class="text-muted small mb-2">
                        <i class="fas fa-map-marker-alt me-1"></i>
                        ${fullLocation || 'Location details'}
                    </div>
                    <div class="bg-light rounded p-2 text-center">
                        <i class="fas fa-image text-muted"></i>
                        <div class="small text-muted">Location Image</div>
                    </div>
                </div>
            `;
        } else {
            preview.innerHTML = `
                <i class="fas fa-map-marker-alt fa-3x mb-3"></i>
                <p>Fill in the form to see a preview</p>
            `;
        }
    }

    // Add event listeners
    [nameInput, countryInput, stateInput, cityInput].forEach(input => {
        input.addEventListener('input', updatePreview);
    });

    // Handle image preview
    imageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const imagePreview = preview.querySelector('.bg-light');
                if (imagePreview) {
                    imagePreview.innerHTML = `
                        <img src="${e.target.result}" class="img-fluid rounded" style="max-height: 100px;">
                    `;
                }
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>
@endpush
