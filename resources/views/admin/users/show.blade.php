@extends('admin.layouts.app')

@section('title', 'User Details - ' . $user->name)
@section('page-title', 'User Details')

@section('content')
<div class="row mb-4">
    <div class="col-md-8">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.users.index') }}">Users</a></li>
                <li class="breadcrumb-item active">{{ $user->name }}</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-primary me-2">
            <i class="fas fa-edit me-1"></i>Edit User
        </a>
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#creditModal">
            <i class="fas fa-coins me-1"></i>Manage Credits
        </button>
    </div>
</div>

<div class="row">
    <!-- User Profile Card -->
    <div class="col-lg-4 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <img src="{{ $user->avatar_url }}" 
                     class="rounded-circle mb-3" 
                     width="120" height="120" 
                     alt="{{ $user->name }}">
                <h4 class="mb-1">{{ $user->name }}</h4>
                <p class="text-muted mb-3">{{ $user->email }}</p>
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h5 class="mb-0" style="color: @primaryColor();">{{ $user->credits }}</h5>
                            <small class="text-muted">Credits</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h5 class="mb-0" style="color: @secondaryColor();">{{ $stats['total_bookings'] }}</h5>
                        <small class="text-muted">Bookings</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-white border-0">
                <h6 class="mb-0">Contact Information</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label text-muted">Email</label>
                    <div>{{ $user->email }}</div>
                </div>
                @if($user->phone)
                <div class="mb-3">
                    <label class="form-label text-muted">Phone</label>
                    <div>{{ $user->phone }}</div>
                </div>
                @endif
                <div class="mb-3">
                    <label class="form-label text-muted">Member Since</label>
                    <div>{{ $user->created_at->format('F d, Y') }}</div>
                </div>
                <div class="mb-0">
                    <label class="form-label text-muted">Last Updated</label>
                    <div>{{ $user->updated_at->diffForHumans() }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics and Activity -->
    <div class="col-lg-8">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, @primaryColor() 0%, #FFE55C 100%);">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-check fa-2x text-dark mb-2"></i>
                        <h4 class="fw-bold text-dark mb-0">{{ $stats['total_bookings'] }}</h4>
                        <small class="text-dark">Total Bookings</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, @secondaryColor() 0%, #90EE90 100%);">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x text-white mb-2"></i>
                        <h4 class="fw-bold text-white mb-0">{{ $stats['confirmed_bookings'] }}</h4>
                        <small class="text-white">Confirmed</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #17a2b8 0%, #7dd3fc 100%);">
                    <div class="card-body text-center">
                        <i class="fas fa-coins fa-2x text-white mb-2"></i>
                        <h4 class="fw-bold text-white mb-0">{{ abs($stats['total_spent']) }}</h4>
                        <small class="text-white">Credits Spent</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #6f42c1 0%, #a855f7 100%);">
                    <div class="card-body text-center">
                        <i class="fas fa-star fa-2x text-white mb-2"></i>
                        <h4 class="fw-bold text-white mb-0">{{ number_format($stats['avg_rating'] ?? 0, 1) }}</h4>
                        <small class="text-white">Avg Rating</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Bookings -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-0">
                <h6 class="mb-0">Recent Bookings</h6>
            </div>
            <div class="card-body">
                @if($user->bookings->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Property</th>
                                    <th>Dates</th>
                                    <th>Status</th>
                                    <th>Credits</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($user->bookings->take(5) as $booking)
                                <tr>
                                    <td>
                                        <div class="fw-medium">{{ $booking->property->title }}</div>
                                        <small class="text-muted">{{ $booking->booking_reference }}</small>
                                    </td>
                                    <td>
                                        <div>{{ $booking->check_in_date->format('M d') }} - {{ $booking->check_out_date->format('M d, Y') }}</div>
                                        <small class="text-muted">{{ $booking->check_in_date->diffInDays($booking->check_out_date) }} nights</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $booking->status === 'confirmed' ? 'success' : ($booking->status === 'pending' ? 'warning' : 'danger') }}">
                                            {{ ucfirst($booking->status) }}
                                        </span>
                                    </td>
                                    <td>{{ $booking->check_in_date->diffInDays($booking->check_out_date) }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-3">
                        <i class="fas fa-calendar-times fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">No bookings yet</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Credit Transactions -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <h6 class="mb-0">Recent Credit Transactions</h6>
            </div>
            <div class="card-body">
                @if($user->creditTransactions->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Amount</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($user->creditTransactions->take(10) as $transaction)
                                <tr>
                                    <td>{{ $transaction->created_at->format('M d, Y') }}</td>
                                    <td>
                                        <span class="badge bg-{{ $transaction->type === 'credit' ? 'success' : 'danger' }}">
                                            {{ $transaction->type === 'credit' ? '+' : '-' }}{{ $transaction->amount }}
                                        </span>
                                    </td>
                                    <td>{{ $transaction->amount }}</td>
                                    <td>{{ $transaction->description }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-3">
                        <i class="fas fa-coins fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">No credit transactions yet</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Credit Management Modal -->
<div class="modal fade" id="creditModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Manage Credits - {{ $user->name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('admin.users.manage-credits', $user) }}">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Current Credits</label>
                        <input type="text" class="form-control" value="{{ $user->credits }}" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Action</label>
                        <select name="action" class="form-select" required>
                            <option value="">Select Action</option>
                            <option value="add">Add Credits</option>
                            <option value="deduct">Deduct Credits</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Amount</label>
                        <input type="number" name="amount" class="form-control" min="1" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea name="description" class="form-control" rows="3" required 
                                  placeholder="Reason for credit adjustment..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Credits</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
