<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Admin Panel') - {{ config('app.name', 'Oasis Homestay') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased">
    <div class="min-vh-100 d-flex">
        <!-- Sidebar -->
        <nav class="sidebar bg-dark text-white" style="width: 250px; min-height: 100vh;">
            <div class="p-3">
                <h4 class="text-center mb-4" style="color: @primaryColor();">
                    <i class="fas fa-home me-2"></i>
                    Oasis Admin
                </h4>
                
                <ul class="nav flex-column">
                    <li class="nav-item mb-2">
                        <a href="{{ route('admin.dashboard') }}" 
                           class="nav-link text-white {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item mb-2">
                        <a href="{{ route('admin.properties.index') }}"
                           class="nav-link text-white {{ request()->routeIs('admin.properties.*') ? 'active' : '' }}">
                            <i class="fas fa-building me-2"></i>
                            Properties
                        </a>
                    </li>
                    <li class="nav-item mb-2">
                        <a href="{{ route('admin.locations.index') }}"
                           class="nav-link text-white {{ request()->routeIs('admin.locations.*') ? 'active' : '' }}">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            Locations
                        </a>
                    </li>
                    <li class="nav-item mb-2">
                        <a href="{{ route('admin.bookings.index') }}"
                           class="nav-link text-white {{ request()->routeIs('admin.bookings.*') ? 'active' : '' }}">
                            <i class="fas fa-calendar-check me-2"></i>
                            Bookings
                        </a>
                    </li>
                    <li class="nav-item mb-2">
                        <a href="{{ route('admin.users.index') }}"
                           class="nav-link text-white {{ request()->routeIs('admin.users.*') ? 'active' : '' }}">
                            <i class="fas fa-users me-2"></i>
                            Users
                        </a>
                    </li>
                    <li class="nav-item mb-2">
                        <a href="{{ route('admin.credits.index') }}"
                           class="nav-link text-white {{ request()->routeIs('admin.credits.*') ? 'active' : '' }}">
                            <i class="fas fa-coins me-2"></i>
                            Credits
                        </a>
                    </li>
                    <li class="nav-item mb-2">
                        <a href="{{ route('admin.reviews.index') }}"
                           class="nav-link text-white {{ request()->routeIs('admin.reviews.*') ? 'active' : '' }}">
                            <i class="fas fa-star me-2"></i>
                            Reviews
                        </a>
                    </li>
                    <li class="nav-item mb-2">
                        <a href="#" class="nav-link text-white">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            Locations
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- User Info -->
            <div class="mt-auto p-3 border-top border-secondary">
                <div class="d-flex align-items-center">
                    <img src="{{ auth()->user()->avatar_url }}" 
                         alt="Admin Avatar" 
                         class="rounded-circle me-2" 
                         width="32" height="32">
                    <div class="flex-grow-1">
                        <div class="small text-white">{{ auth()->user()->name }}</div>
                        <div class="small text-muted">Administrator</div>
                    </div>
                </div>
                <form method="POST" action="{{ route('admin.logout') }}" class="mt-2">
                    @csrf
                    <button type="submit" class="btn btn-outline-light btn-sm w-100">
                        <i class="fas fa-sign-out-alt me-1"></i>
                        Logout
                    </button>
                </form>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="flex-grow-1">
            <!-- Top Navigation -->
            <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom">
                <div class="container-fluid">
                    <h5 class="mb-0">@yield('page-title', 'Dashboard')</h5>
                    
                    <div class="d-flex align-items-center">
                        <span class="text-muted me-3">
                            <i class="fas fa-clock me-1"></i>
                            {{ now()->format('M d, Y - H:i') }}
                        </span>
                        <a href="{{ url('/') }}" 
                           class="btn btn-outline-primary btn-sm" 
                           target="_blank">
                            <i class="fas fa-external-link-alt me-1"></i>
                            View Site
                        </a>
                    </div>
                </div>
            </nav>

            <!-- Page Content -->
            <main class="p-4">
                @if (session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if (session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if ($errors->any())
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Please fix the following errors:</strong>
                        <ul class="mb-0 mt-2">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @yield('content')
            </main>
        </div>
    </div>

    <style>
        .sidebar .nav-link.active {
            background-color: @primaryColor();
            color: #000 !important;
            border-radius: 0.375rem;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255, 215, 0, 0.1);
            border-radius: 0.375rem;
        }
    </style>

    <!-- Additional Scripts -->
    @stack('scripts')
</body>
</html>
