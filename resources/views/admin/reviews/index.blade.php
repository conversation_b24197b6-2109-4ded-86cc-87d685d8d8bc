@extends('admin.layouts.app')

@section('title', 'Reviews Management')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Reviews Management</h1>
            <p class="text-muted">Manage property reviews and ratings</p>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3" style="background: linear-gradient(135deg, var(--primary-color), var(--accent-color));">
                                <i class="fas fa-star text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold h4 mb-0">{{ number_format($stats['total_reviews']) }}</div>
                            <div class="text-muted small">Total Reviews</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3 bg-warning bg-opacity-10">
                                <i class="fas fa-clock text-warning"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold h4 mb-0">{{ number_format($stats['pending_reviews']) }}</div>
                            <div class="text-muted small">Pending Reviews</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3 bg-success bg-opacity-10">
                                <i class="fas fa-check text-success"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold h4 mb-0">{{ number_format($stats['approved_reviews']) }}</div>
                            <div class="text-muted small">Approved Reviews</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle p-3 bg-info bg-opacity-10">
                                <i class="fas fa-star-half-alt text-info"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold h4 mb-0">{{ number_format($stats['average_rating'], 1) }}</div>
                            <div class="text-muted small">Average Rating</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.reviews.index') }}" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Search</label>
                    <input type="text" class="form-control" name="search" value="{{ request('search') }}" 
                           placeholder="Search reviews, users, properties...">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Status</label>
                    <select class="form-select" name="status">
                        <option value="">All Statuses</option>
                        <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>Approved</option>
                        <option value="rejected" {{ request('status') === 'rejected' ? 'selected' : '' }}>Rejected</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Rating</label>
                    <select class="form-select" name="rating">
                        <option value="">All Ratings</option>
                        @for($i = 5; $i >= 1; $i--)
                            <option value="{{ $i }}" {{ request('rating') == $i ? 'selected' : '' }}>
                                {{ $i }} Star{{ $i > 1 ? 's' : '' }}
                            </option>
                        @endfor
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Property</label>
                    <select class="form-select" name="property_id">
                        <option value="">All Properties</option>
                        @foreach($properties as $property)
                            <option value="{{ $property->id }}" {{ request('property_id') == $property->id ? 'selected' : '' }}>
                                {{ $property->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>Filter
                        </button>
                        <a href="{{ route('admin.reviews.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Reviews Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-bottom-0 py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Reviews ({{ $reviews->total() }})</h5>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-success btn-sm" onclick="bulkAction('approve')" id="bulk-approve" disabled>
                        <i class="fas fa-check me-1"></i>Approve Selected
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" onclick="bulkAction('reject')" id="bulk-reject" disabled>
                        <i class="fas fa-times me-1"></i>Reject Selected
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" onclick="bulkAction('delete')" id="bulk-delete" disabled>
                        <i class="fas fa-trash me-1"></i>Delete Selected
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            @if($reviews->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="40">
                                    <input type="checkbox" class="form-check-input" id="select-all">
                                </th>
                                <th>Review</th>
                                <th>Property</th>
                                <th>Rating</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($reviews as $review)
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input review-checkbox" value="{{ $review->id }}">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-start">
                                            <img src="{{ $review->user->avatar_url }}" class="rounded-circle me-3" width="40" height="40" alt="{{ $review->user->name }}">
                                            <div>
                                                <div class="fw-semibold">{{ $review->user->name }}</div>
                                                <div class="text-muted small">{{ $review->user->email }}</div>
                                                <div class="mt-1">{{ Str::limit($review->comment, 100) }}</div>
                                                @if($review->images && count($review->images) > 0)
                                                    <div class="mt-1">
                                                        <small class="text-muted">
                                                            <i class="fas fa-image me-1"></i>{{ count($review->images) }} image(s)
                                                        </small>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="fw-semibold">{{ $review->property->name }}</div>
                                        <div class="text-muted small">{{ $review->property->location->name ?? 'N/A' }}</div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @for($i = 1; $i <= 5; $i++)
                                                <i class="fas fa-star {{ $i <= $review->rating ? 'text-warning' : 'text-muted' }}"></i>
                                            @endfor
                                            <span class="ms-2 fw-semibold">{{ $review->rating }}/5</span>
                                        </div>
                                    </td>
                                    <td>
                                        @if($review->status === 'pending')
                                            <span class="badge bg-warning">Pending</span>
                                        @elseif($review->status === 'approved')
                                            <span class="badge bg-success">Approved</span>
                                        @else
                                            <span class="badge bg-danger">Rejected</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div>{{ $review->created_at->format('M d, Y') }}</div>
                                        <div class="text-muted small">{{ $review->created_at->format('h:i A') }}</div>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <a href="{{ route('admin.reviews.show', $review) }}" class="btn btn-sm btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <form method="POST" action="{{ route('admin.reviews.destroy', $review) }}" class="d-inline" 
                                                  onsubmit="return confirm('Are you sure you want to delete this review?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-star fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No reviews found</h5>
                    <p class="text-muted">No reviews match your current filters.</p>
                </div>
            @endif
        </div>
        @if($reviews->hasPages())
            <div class="card-footer bg-white border-top-0">
                {{ $reviews->links() }}
            </div>
        @endif
    </div>
</div>

<!-- Bulk Action Form -->
<form id="bulk-form" method="POST" action="{{ route('admin.reviews.bulk-update') }}" style="display: none;">
    @csrf
    <input type="hidden" name="action" id="bulk-action">
    <div id="bulk-review-ids"></div>
</form>

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAll = document.getElementById('select-all');
    const checkboxes = document.querySelectorAll('.review-checkbox');
    const bulkButtons = ['bulk-approve', 'bulk-reject', 'bulk-delete'];

    // Select all functionality
    selectAll.addEventListener('change', function() {
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        toggleBulkButtons();
    });

    // Individual checkbox change
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.review-checkbox:checked').length;
            selectAll.checked = checkedCount === checkboxes.length;
            selectAll.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
            toggleBulkButtons();
        });
    });

    function toggleBulkButtons() {
        const checkedCount = document.querySelectorAll('.review-checkbox:checked').length;
        bulkButtons.forEach(buttonId => {
            document.getElementById(buttonId).disabled = checkedCount === 0;
        });
    }
});

function bulkAction(action) {
    const checkedBoxes = document.querySelectorAll('.review-checkbox:checked');
    if (checkedBoxes.length === 0) {
        alert('Please select at least one review.');
        return;
    }

    let message = '';
    switch(action) {
        case 'approve':
            message = `Are you sure you want to approve ${checkedBoxes.length} review(s)?`;
            break;
        case 'reject':
            message = `Are you sure you want to reject ${checkedBoxes.length} review(s)?`;
            break;
        case 'delete':
            message = `Are you sure you want to delete ${checkedBoxes.length} review(s)? This action cannot be undone.`;
            break;
    }

    if (confirm(message)) {
        const form = document.getElementById('bulk-form');
        const actionInput = document.getElementById('bulk-action');
        const idsContainer = document.getElementById('bulk-review-ids');

        actionInput.value = action;
        idsContainer.innerHTML = '';

        checkedBoxes.forEach(checkbox => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'review_ids[]';
            input.value = checkbox.value;
            idsContainer.appendChild(input);
        });

        form.submit();
    }
}
</script>
@endpush
