@extends('layouts.admin')

@section('title', 'Review Details')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Review Details</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.reviews.index') }}">Reviews</a></li>
                    <li class="breadcrumb-item active">Review #{{ $review->id }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.reviews.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Reviews
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Review Details -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Review Information</h5>
                        <div class="d-flex align-items-center gap-2">
                            @if($review->status === 'pending')
                                <span class="badge bg-warning">Pending</span>
                            @elseif($review->status === 'approved')
                                <span class="badge bg-success">Approved</span>
                            @else
                                <span class="badge bg-danger">Rejected</span>
                            @endif
                            <div class="d-flex align-items-center">
                                @for($i = 1; $i <= 5; $i++)
                                    <i class="fas fa-star {{ $i <= $review->rating ? 'text-warning' : 'text-muted' }}"></i>
                                @endfor
                                <span class="ms-2 fw-semibold">{{ $review->rating }}/5</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- User Information -->
                    <div class="d-flex align-items-center mb-4">
                        <img src="{{ $review->user->avatar_url }}" class="rounded-circle me-3" width="60" height="60" alt="{{ $review->user->name }}">
                        <div>
                            <h6 class="mb-1">{{ $review->user->name }}</h6>
                            <div class="text-muted">{{ $review->user->email }}</div>
                            <div class="text-muted small">
                                <i class="fas fa-calendar me-1"></i>Reviewed on {{ $review->created_at->format('M d, Y \a\t h:i A') }}
                            </div>
                        </div>
                    </div>

                    <!-- Property Information -->
                    <div class="mb-4">
                        <h6 class="text-muted mb-2">Property</h6>
                        <div class="d-flex align-items-center">
                            @if($review->property->primary_image_url)
                                <img src="{{ $review->property->primary_image_url }}" class="rounded me-3" width="80" height="60" style="object-fit: cover;" alt="{{ $review->property->name }}">
                            @endif
                            <div>
                                <h6 class="mb-1">{{ $review->property->name }}</h6>
                                <div class="text-muted">{{ $review->property->location->name ?? 'N/A' }}</div>
                                <div class="text-muted small">{{ $review->property->address }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Booking Information -->
                    @if($review->booking)
                        <div class="mb-4">
                            <h6 class="text-muted mb-2">Booking Details</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="small text-muted">Booking Reference</div>
                                    <div class="fw-semibold">{{ $review->booking->booking_reference }}</div>
                                </div>
                                <div class="col-md-6">
                                    <div class="small text-muted">Stay Period</div>
                                    <div class="fw-semibold">
                                        {{ $review->booking->check_in_date->format('M d, Y') }} - 
                                        {{ $review->booking->check_out_date->format('M d, Y') }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Review Comment -->
                    <div class="mb-4">
                        <h6 class="text-muted mb-2">Review Comment</h6>
                        <div class="bg-light p-3 rounded">
                            {{ $review->comment }}
                        </div>
                    </div>

                    <!-- Review Images -->
                    @if($review->images && count($review->images) > 0)
                        <div class="mb-4">
                            <h6 class="text-muted mb-2">Review Images ({{ count($review->images) }})</h6>
                            <div class="row g-2">
                                @foreach($review->images as $image)
                                    <div class="col-md-3">
                                        <img src="{{ asset('storage/' . $image) }}" class="img-fluid rounded" style="height: 150px; width: 100%; object-fit: cover;" 
                                             alt="Review Image" data-bs-toggle="modal" data-bs-target="#imageModal" 
                                             onclick="showImage('{{ asset('storage/' . $image) }}')">
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Admin Notes -->
                    @if($review->admin_notes)
                        <div class="mb-4">
                            <h6 class="text-muted mb-2">Admin Notes</h6>
                            <div class="bg-warning bg-opacity-10 p-3 rounded border-start border-warning border-4">
                                {{ $review->admin_notes }}
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Actions Sidebar -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom py-3">
                    <h5 class="mb-0">Review Actions</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.reviews.update-status', $review) }}">
                        @csrf
                        @method('PATCH')
                        
                        <div class="mb-3">
                            <label class="form-label">Status</label>
                            <select class="form-select" name="status" required>
                                <option value="pending" {{ $review->status === 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="approved" {{ $review->status === 'approved' ? 'selected' : '' }}>Approved</option>
                                <option value="rejected" {{ $review->status === 'rejected' ? 'selected' : '' }}>Rejected</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Admin Notes</label>
                            <textarea class="form-control" name="admin_notes" rows="4" 
                                      placeholder="Add notes about this review...">{{ $review->admin_notes }}</textarea>
                            <div class="form-text">Internal notes visible only to administrators.</div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Update Review
                            </button>
                        </div>
                    </form>

                    <hr>

                    <!-- Quick Actions -->
                    <div class="d-grid gap-2">
                        @if($review->status !== 'approved')
                            <form method="POST" action="{{ route('admin.reviews.update-status', $review) }}" class="d-inline">
                                @csrf
                                @method('PATCH')
                                <input type="hidden" name="status" value="approved">
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-check me-1"></i>Approve Review
                                </button>
                            </form>
                        @endif

                        @if($review->status !== 'rejected')
                            <form method="POST" action="{{ route('admin.reviews.update-status', $review) }}" class="d-inline">
                                @csrf
                                @method('PATCH')
                                <input type="hidden" name="status" value="rejected">
                                <button type="submit" class="btn btn-warning w-100">
                                    <i class="fas fa-times me-1"></i>Reject Review
                                </button>
                            </form>
                        @endif

                        <form method="POST" action="{{ route('admin.reviews.destroy', $review) }}" 
                              onsubmit="return confirm('Are you sure you want to delete this review? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="fas fa-trash me-1"></i>Delete Review
                            </button>
                        </form>
                    </div>

                    <!-- Review Statistics -->
                    <hr>
                    <div class="small text-muted">
                        <div class="d-flex justify-content-between mb-1">
                            <span>Created:</span>
                            <span>{{ $review->created_at->format('M d, Y h:i A') }}</span>
                        </div>
                        @if($review->approved_at)
                            <div class="d-flex justify-content-between mb-1">
                                <span>Approved:</span>
                                <span>{{ $review->approved_at->format('M d, Y h:i A') }}</span>
                            </div>
                        @endif
                        <div class="d-flex justify-content-between">
                            <span>Last Updated:</span>
                            <span>{{ $review->updated_at->format('M d, Y h:i A') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Review Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid rounded" alt="Review Image">
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function showImage(src) {
    document.getElementById('modalImage').src = src;
}
</script>
@endpush
