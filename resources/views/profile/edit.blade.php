@extends('layouts.frontend')

@section('title', 'Profile Settings')

@section('content')
<div class="container py-5">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}" style="color: @secondaryColor();">Dashboard</a></li>
                    <li class="breadcrumb-item active">Profile Settings</li>
                </ol>
            </nav>
            <h2 class="mb-1" style="color: @secondaryColor();">
                <i class="fas fa-user-cog me-2" style="color: @primaryColor();"></i>
                Profile Settings
            </h2>
            <p class="text-muted">Manage your account information and preferences</p>
        </div>
    </div>

    <div class="row">
        <!-- Profile Information -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0" style="color: @secondaryColor();">
                        <i class="fas fa-user me-2"></i>Profile Information
                    </h5>
                </div>
                <div class="card-body">
                    @include('profile.partials.update-profile-information-form')
                </div>
            </div>

            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0" style="color: @secondaryColor();">
                        <i class="fas fa-lock me-2"></i>Update Password
                    </h5>
                </div>
                <div class="card-body">
                    @include('profile.partials.update-password-form')
                </div>
            </div>

            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0 text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Delete Account
                    </h5>
                </div>
                <div class="card-body">
                    @include('profile.partials.delete-user-form')
                </div>
            </div>
        </div>

        <!-- Account Summary Sidebar -->
        <div class="col-lg-4">
            <!-- Account Overview -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0" style="color: @secondaryColor();">
                        <i class="fas fa-chart-pie me-2"></i>Account Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <img src="{{ auth()->user()->avatar_url }}"
                             class="rounded-circle mb-3"
                             width="80" height="80"
                             alt="{{ auth()->user()->name }}">
                        <h6 class="mb-1">{{ auth()->user()->name }}</h6>
                        <p class="text-muted small mb-0">{{ auth()->user()->email }}</p>
                    </div>

                    <hr>

                    <div class="row text-center">
                        <div class="col-6">
                            <div class="mb-3">
                                <h4 class="mb-1" style="color: @primaryColor();">{{ auth()->user()->credits }}</h4>
                                <small class="text-muted">Credits</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-3">
                                <h4 class="mb-1" style="color: @secondaryColor();">{{ auth()->user()->bookings()->count() }}</h4>
                                <small class="text-muted">Bookings</small>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <a href="{{ route('credits.index') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-coins me-2"></i>View Credit History
                        </a>
                        <a href="{{ route('bookings.index') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-calendar-check me-2"></i>View Bookings
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0" style="color: @secondaryColor();">
                        <i class="fas fa-clock me-2"></i>Recent Activity
                    </h5>
                </div>
                <div class="card-body">
                    @php
                        $recentTransactions = auth()->user()->creditTransactions()
                            ->orderBy('created_at', 'desc')
                            ->limit(5)
                            ->get();
                    @endphp

                    @if($recentTransactions->count() > 0)
                        @foreach($recentTransactions as $transaction)
                        <div class="d-flex align-items-center mb-3 {{ !$loop->last ? 'pb-3 border-bottom' : '' }}">
                            <div class="flex-shrink-0 me-3">
                                @if($transaction->type === 'credit')
                                    <div class="bg-success rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                        <i class="fas fa-plus text-white small"></i>
                                    </div>
                                @else
                                    <div class="bg-danger rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                        <i class="fas fa-minus text-white small"></i>
                                    </div>
                                @endif
                            </div>
                            <div class="flex-grow-1">
                                <p class="mb-1 small">{{ Str::limit($transaction->description, 30) }}</p>
                                <small class="text-muted">{{ $transaction->created_at->diffForHumans() }}</small>
                            </div>
                            <div class="flex-shrink-0">
                                @if($transaction->type === 'credit')
                                    <span class="badge bg-success">+{{ $transaction->amount }}</span>
                                @else
                                    <span class="badge bg-danger">{{ $transaction->amount }}</span>
                                @endif
                            </div>
                        </div>
                        @endforeach

                        <div class="text-center mt-3">
                            <a href="{{ route('credits.index') }}" class="btn btn-outline-primary btn-sm">
                                View All Transactions
                            </a>
                        </div>
                    @else
                        <div class="text-center py-3">
                            <i class="fas fa-history fa-2x text-muted mb-2"></i>
                            <p class="text-muted small mb-0">No recent activity</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.btn-primary {
    background-color: @secondaryColor();
    border-color: @secondaryColor();
}

.btn-primary:hover {
    background-color: #236B47;
    border-color: #236B47;
}

.btn-outline-primary {
    color: @secondaryColor();
    border-color: @secondaryColor();
}

.btn-outline-primary:hover {
    background-color: @secondaryColor();
    border-color: @secondaryColor();
}

.breadcrumb-item + .breadcrumb-item::before {
    color: @secondaryColor();
}

.card {
    border-radius: 10px;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
}
</style>
@endsection
