@extends('layouts.frontend')

@section('title', 'Dashboard')

@section('content')
<div class="container py-5">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1" style="color: @secondaryColor();">
                        Welcome back, {{ auth()->user()->name }}!
                    </h2>
                    <p class="text-muted mb-0">Manage your bookings and account settings</p>
                </div>
                <div class="d-none d-md-block">
                    <img src="{{ auth()->user()->avatar_url }}"
                         class="rounded-circle"
                         width="60" height="60"
                         alt="{{ auth()->user()->name }}">
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-5">
        <!-- Credit Balance Card -->
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, @primaryColor() 0%, #FFE55C 100%);">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-coins fa-2x text-dark"></i>
                    </div>
                    <h3 class="fw-bold text-dark mb-1">{{ auth()->user()->credits }}</h3>
                    <p class="mb-0 text-dark">Available Credits</p>
                    @if(auth()->user()->credits < 3)
                        <small class="text-danger fw-bold">Low Balance!</small>
                    @endif
                </div>
            </div>
        </div>

        <!-- Total Bookings Card -->
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-calendar-check fa-2x" style="color: @secondaryColor();"></i>
                    </div>
                    <h3 class="fw-bold mb-1" style="color: @secondaryColor();">{{ auth()->user()->bookings()->count() }}</h3>
                    <p class="mb-0 text-muted">Total Bookings</p>
                </div>
            </div>
        </div>

        <!-- Credits Used Card -->
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-chart-line fa-2x" style="color: @primaryColor();"></i>
                    </div>
                    <h3 class="fw-bold mb-1" style="color: @primaryColor();">{{ auth()->user()->creditTransactions()->debits()->sum('amount') * -1 }}</h3>
                    <p class="mb-0 text-muted">Credits Used</p>
                </div>
            </div>
        </div>

        <!-- Active Bookings Card -->
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-bed fa-2x" style="color: @secondaryColor();"></i>
                    </div>
                    <h3 class="fw-bold mb-1" style="color: @secondaryColor();">{{ auth()->user()->bookings()->where('status', 'confirmed')->whereDate('check_out_date', '>=', now())->count() }}</h3>
                    <p class="mb-0 text-muted">Active Bookings</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Bookings -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0" style="color: @secondaryColor();">
                            <i class="fas fa-history me-2"></i>Recent Bookings
                        </h5>
                        <a href="{{ route('bookings.index') }}" class="btn btn-outline-primary btn-sm">
                            View All
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @php
                        $recentBookings = auth()->user()->bookings()
                            ->with(['property.location'])
                            ->orderBy('created_at', 'desc')
                            ->limit(5)
                            ->get();
                    @endphp

                    @if($recentBookings->count() > 0)
                        @foreach($recentBookings as $booking)
                        <div class="d-flex align-items-center mb-3 pb-3 {{ !$loop->last ? 'border-bottom' : '' }}">
                            <div class="flex-shrink-0 me-3">
                                <img src="{{ $booking->property->primary_image_url }}"
                                     class="rounded"
                                     width="60" height="60"
                                     style="object-fit: cover;"
                                     alt="{{ $booking->property->name }}">
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ Str::limit($booking->property->name, 30) }}</h6>
                                <p class="mb-1 text-muted small">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    {{ $booking->property->location->name }}
                                </p>
                                <p class="mb-0 small">
                                    <strong>{{ $booking->check_in_date->format('M d') }}</strong> -
                                    <strong>{{ $booking->check_out_date->format('M d, Y') }}</strong>
                                    <span class="ms-2">
                                        @if($booking->status === 'confirmed')
                                            <span class="badge bg-success">Confirmed</span>
                                        @elseif($booking->status === 'pending')
                                            <span class="badge bg-warning">Pending</span>
                                        @elseif($booking->status === 'cancelled')
                                            <span class="badge bg-danger">Cancelled</span>
                                        @endif
                                    </span>
                                </p>
                            </div>
                            <div class="flex-shrink-0">
                                <a href="{{ route('bookings.show', $booking) }}"
                                   class="btn btn-outline-primary btn-sm">
                                    View
                                </a>
                            </div>
                        </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No bookings yet</h6>
                            <p class="text-muted mb-3">Start exploring our amazing properties!</p>
                            <a href="{{ route('properties.search') }}" class="btn btn-primary">
                                Browse Properties
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Quick Actions & Account Info -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0" style="color: @secondaryColor();">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('properties.search') }}" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Browse Properties
                        </a>
                        <a href="{{ route('bookings.index') }}" class="btn btn-outline-primary">
                            <i class="fas fa-calendar-check me-2"></i>My Bookings
                        </a>
                        <a href="{{ route('profile.edit') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-user-edit me-2"></i>Edit Profile
                        </a>
                    </div>
                </div>
            </div>

            <!-- Account Summary -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0" style="color: @secondaryColor();">
                        <i class="fas fa-user-circle me-2"></i>Account Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Member since</small><br>
                        <strong>{{ auth()->user()->created_at->format('M Y') }}</strong>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Email</small><br>
                        <strong>{{ auth()->user()->email }}</strong>
                    </div>
                    @if(auth()->user()->phone)
                    <div class="mb-3">
                        <small class="text-muted">Phone</small><br>
                        <strong>{{ auth()->user()->phone }}</strong>
                    </div>
                    @endif
                    <div class="mb-0">
                        <small class="text-muted">Account Status</small><br>
                        <span class="badge bg-success">Active</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.btn-primary {
    background-color: @secondaryColor();
    border-color: @secondaryColor();
}

.btn-primary:hover {
    background-color: #236B47;
    border-color: #236B47;
}

.btn-outline-primary {
    color: @secondaryColor();
    border-color: @secondaryColor();
}

.btn-outline-primary:hover {
    background-color: @secondaryColor();
    border-color: @secondaryColor();
}

.card {
    border-radius: 10px;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
}
</style>
@endsection
