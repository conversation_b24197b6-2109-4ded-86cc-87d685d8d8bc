@extends('layouts.frontend')

@section('title', 'My Credits')

@section('content')
<div class="container py-5">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}" style="color: @secondaryColor();">Dashboard</a></li>
                    <li class="breadcrumb-item active">My Credits</li>
                </ol>
            </nav>
            <h2 class="mb-1" style="color: @secondaryColor();">
                <i class="fas fa-coins me-2" style="color: @primaryColor();"></i>
                My Credits
            </h2>
            <p class="text-muted">Manage your credit balance and view transaction history</p>
        </div>
    </div>

    <!-- Credit Balance Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, @primaryColor() 0%, #FFE55C 100%);">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="fas fa-wallet fa-3x text-dark"></i>
                                </div>
                                <div>
                                    <h3 class="fw-bold text-dark mb-1">{{ $user->credits }} Credits</h3>
                                    <p class="mb-0 text-dark">Current Balance</p>
                                    @if($user->credits < 3)
                                        <small class="text-danger fw-bold">⚠️ Low Balance - Consider getting more credits</small>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 text-md-end mt-3 mt-md-0">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="text-dark">
                                        <strong>{{ $totalCreditsEarned }}</strong><br>
                                        <small>Earned</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="text-dark">
                                        <strong>{{ $totalCreditsUsed }}</strong><br>
                                        <small>Used</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="text-dark">
                                        <strong>{{ $totalTransactions }}</strong><br>
                                        <small>Transactions</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Actions -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="btn-group" role="group">
                <a href="{{ route('credits.index') }}" 
                   class="btn {{ !request('type') ? 'btn-primary' : 'btn-outline-primary' }}">
                    All Transactions
                </a>
                <a href="{{ route('credits.index', ['type' => 'credit']) }}" 
                   class="btn {{ request('type') === 'credit' ? 'btn-success' : 'btn-outline-success' }}">
                    Credits Earned
                </a>
                <a href="{{ route('credits.index', ['type' => 'debit']) }}" 
                   class="btn {{ request('type') === 'debit' ? 'btn-danger' : 'btn-outline-danger' }}">
                    Credits Used
                </a>
            </div>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="{{ route('properties.search') }}" class="btn btn-primary">
                <i class="fas fa-search me-2"></i>Browse Properties
            </a>
        </div>
    </div>

    <!-- Transaction History -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0" style="color: @secondaryColor();">
                        <i class="fas fa-history me-2"></i>Transaction History
                    </h5>
                </div>
                <div class="card-body p-0">
                    @if($transactions->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Date</th>
                                        <th>Description</th>
                                        <th>Property</th>
                                        <th class="text-center">Amount</th>
                                        <th class="text-center">Balance</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($transactions as $transaction)
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ $transaction->created_at->format('M d, Y') }}</strong><br>
                                                <small class="text-muted">{{ $transaction->created_at->format('g:i A') }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                {{ $transaction->description }}
                                                @if($transaction->booking_id)
                                                    <br><small class="text-muted">Booking #{{ $transaction->booking_id }}</small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            @if($transaction->booking && $transaction->booking->property)
                                                <div class="d-flex align-items-center">
                                                    <img src="{{ $transaction->booking->property->primary_image_url }}" 
                                                         class="rounded me-2" 
                                                         width="40" height="40"
                                                         style="object-fit: cover;"
                                                         alt="{{ $transaction->booking->property->name }}">
                                                    <div>
                                                        <strong>{{ Str::limit($transaction->booking->property->name, 25) }}</strong><br>
                                                        <small class="text-muted">{{ $transaction->booking->property->location->name ?? '' }}</small>
                                                    </div>
                                                </div>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            @if($transaction->type === 'credit')
                                                <span class="badge bg-success fs-6">+{{ $transaction->amount }}</span>
                                            @else
                                                <span class="badge bg-danger fs-6">{{ $transaction->amount }}</span>
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            <strong>{{ $user->credits }}</strong>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        @if($transactions->hasPages())
                            <div class="card-footer bg-white border-0">
                                {{ $transactions->links() }}
                            </div>
                        @endif
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No transactions yet</h6>
                            <p class="text-muted mb-3">Your credit transactions will appear here</p>
                            <a href="{{ route('properties.search') }}" class="btn btn-primary">
                                Start Booking
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.btn-primary {
    background-color: @secondaryColor();
    border-color: @secondaryColor();
}

.btn-primary:hover {
    background-color: #236B47;
    border-color: #236B47;
}

.btn-outline-primary {
    color: @secondaryColor();
    border-color: @secondaryColor();
}

.btn-outline-primary:hover {
    background-color: @secondaryColor();
    border-color: @secondaryColor();
}

.breadcrumb-item + .breadcrumb-item::before {
    color: @secondaryColor();
}

.card {
    border-radius: 10px;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: @secondaryColor();
}
</style>
@endsection
