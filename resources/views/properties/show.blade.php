@extends('layouts.frontend')

@section('title', $property->name . ' - Oasis Homestay')

@section('content')
<div class="container py-4">
    <!-- Property Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('properties.search') }}">Properties</a></li>
                    <li class="breadcrumb-item active">{{ $property->name }}</li>
                </ol>
            </nav>
            
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h1 class="h2 mb-2">{{ $property->name }}</h1>
                    <div class="d-flex align-items-center text-muted mb-2">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        <span>{{ $property->address }}, {{ $property->location->name }}</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="text-warning me-2">
                            @for($i = 1; $i <= 5; $i++)
                                <i class="fas fa-star {{ $i <= $property->average_rating ? '' : 'text-muted' }}"></i>
                            @endfor
                        </div>
                        <span class="text-muted">{{ number_format($property->average_rating, 1) }} ({{ $property->reviews_count }} reviews)</span>
                    </div>
                </div>
                <div class="text-end">
                    <div class="h3 mb-0" style="color: @primaryColor();">
                        ${{ number_format($property->price_per_night) }}
                    </div>
                    <small class="text-muted">per night</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Property Images and Details -->
        <div class="col-lg-8">
            <!-- Image Gallery -->
            <div class="mb-4">
                @if($property->images->count() > 0)
                    <div id="propertyCarousel" class="carousel slide" data-bs-ride="carousel">
                        <div class="carousel-inner rounded">
                            @foreach($property->images as $index => $image)
                            <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                                <img src="{{ $image->image_url }}" 
                                     class="d-block w-100" 
                                     alt="{{ $image->alt_text }}"
                                     style="height: 400px; object-fit: cover;">
                            </div>
                            @endforeach
                        </div>
                        @if($property->images->count() > 1)
                        <button class="carousel-control-prev" type="button" data-bs-target="#propertyCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon"></span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#propertyCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon"></span>
                        </button>
                        @endif
                    </div>
                @else
                    <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 400px;">
                        <div class="text-center text-muted">
                            <i class="fas fa-image fa-3x mb-3"></i>
                            <p>No images available</p>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Property Details -->
            <div class="card mb-4">
                <div class="card-body">
                    <h3 class="card-title">About this property</h3>
                    <p class="card-text">{{ $property->description }}</p>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h5>Property Details</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-users me-2"></i>{{ $property->guest_capacity }} guests</li>
                                <li><i class="fas fa-bed me-2"></i>{{ $property->bedrooms }} bedrooms</li>
                                <li><i class="fas fa-bath me-2"></i>{{ $property->bathrooms }} bathrooms</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Amenities</h5>
                            @if($property->amenities && count($property->amenities) > 0)
                                <div class="row">
                                    @foreach($property->amenities as $amenity)
                                    <div class="col-6">
                                        <small><i class="fas fa-check text-success me-1"></i>{{ $amenity }}</small>
                                    </div>
                                    @endforeach
                                </div>
                            @else
                                <p class="text-muted">No amenities listed</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reviews Section -->
            @if($property->reviews->count() > 0)
            <div class="card mb-4">
                <div class="card-body">
                    <h3 class="card-title">Reviews</h3>
                    
                    @foreach($property->reviews->take(5) as $review)
                    <div class="border-bottom pb-3 mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <img src="{{ $review->user->avatar_url }}" 
                                 class="rounded-circle me-3" 
                                 width="40" height="40"
                                 alt="{{ $review->user->name }}">
                            <div>
                                <h6 class="mb-0">{{ $review->user->name }}</h6>
                                <div class="text-warning">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star {{ $i <= $review->rating ? '' : 'text-muted' }}"></i>
                                    @endfor
                                </div>
                            </div>
                            <small class="text-muted ms-auto">{{ $review->created_at->diffForHumans() }}</small>
                        </div>
                        <p class="mb-0">{{ $review->comment }}</p>
                    </div>
                    @endforeach
                    
                    @if($property->reviews->count() > 5)
                    <div class="text-center">
                        <button class="btn btn-outline-primary btn-sm" onclick="loadMoreReviews()">
                            Load More Reviews
                        </button>
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>

        <!-- Booking Sidebar -->
        <div class="col-lg-4">
            <div class="card sticky-top" style="top: 100px;">
                <div class="card-body">
                    <h4 class="card-title">Book this property</h4>
                    
                    <form id="bookingForm" action="{{ route('bookings.store') }}" method="POST">
                        @csrf
                        <input type="hidden" name="property_id" value="{{ $property->id }}">
                        
                        <div class="mb-3">
                            <label for="check_in_date" class="form-label">Check-in</label>
                            <input type="date" 
                                   class="form-control" 
                                   id="check_in_date" 
                                   name="check_in_date"
                                   min="{{ date('Y-m-d') }}"
                                   required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="check_out_date" class="form-label">Check-out</label>
                            <input type="date" 
                                   class="form-control" 
                                   id="check_out_date" 
                                   name="check_out_date"
                                   min="{{ date('Y-m-d', strtotime('+1 day')) }}"
                                   required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="guests" class="form-label">Guests</label>
                            <select class="form-select" id="guests" name="guests" required>
                                @for($i = 1; $i <= $property->guest_capacity; $i++)
                                    <option value="{{ $i }}">{{ $i }} Guest{{ $i > 1 ? 's' : '' }}</option>
                                @endfor
                            </select>
                        </div>
                        
                        <!-- Booking Summary -->
                        <div class="border-top pt-3 mb-3" id="bookingSummary" style="display: none;">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Nights:</span>
                                <span id="nightsCount">0</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>${{ number_format($property->price_per_night) }} x <span id="nightsText">0</span> nights:</span>
                                <span id="subtotal">$0</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Service fee:</span>
                                <span id="serviceFee">$0</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between fw-bold">
                                <span>Total:</span>
                                <span id="totalAmount">$0</span>
                            </div>
                        </div>
                        
                        @auth
                            <button type="submit" class="btn btn-primary w-100 btn-lg">
                                Reserve Now
                            </button>
                        @else
                            <a href="{{ route('login') }}" class="btn btn-primary w-100 btn-lg">
                                Login to Book
                            </a>
                        @endauth
                    </form>
                    
                    <div class="text-center mt-3">
                        <small class="text-muted">You won't be charged yet</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Similar Properties -->
    @if($similarProperties->count() > 0)
    <div class="row mt-5">
        <div class="col-12">
            <h3 class="mb-4">Similar properties in {{ $property->location->name }}</h3>
            <div class="row">
                @foreach($similarProperties as $similar)
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card property-card h-100 shadow-sm border-0">
                        <div class="position-relative">
                            <img src="{{ $similar->primary_image_url }}" 
                                 class="card-img-top" 
                                 alt="{{ $similar->name }}"
                                 style="height: 200px; object-fit: cover;">
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title">{{ Str::limit($similar->name, 30) }}</h6>
                            <div class="d-flex justify-content-between align-items-center mt-auto">
                                <div>
                                    <span class="fw-bold" style="color: @primaryColor();">
                                        ${{ number_format($similar->price_per_night) }}
                                    </span>
                                    <small class="text-muted">/night</small>
                                </div>
                                <a href="{{ route('properties.show', $similar->slug) }}" 
                                   class="btn btn-outline-primary btn-sm">
                                    View
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif
</div>

<style>
.property-card:hover {
    transform: translateY(-3px);
    transition: transform 0.3s ease;
}

.btn-primary {
    background-color: @secondaryColor();
    border-color: @secondaryColor();
}

.btn-primary:hover {
    background-color: #236B47;
    border-color: #236B47;
}

.btn-outline-primary {
    color: @secondaryColor();
    border-color: @secondaryColor();
}

.btn-outline-primary:hover {
    background-color: @secondaryColor();
    border-color: @secondaryColor();
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkInInput = document.getElementById('check_in_date');
    const checkOutInput = document.getElementById('check_out_date');
    const bookingSummary = document.getElementById('bookingSummary');
    const pricePerNight = {{ $property->price_per_night }};
    
    function calculateBooking() {
        const checkIn = new Date(checkInInput.value);
        const checkOut = new Date(checkOutInput.value);
        
        if (checkInInput.value && checkOutInput.value && checkOut > checkIn) {
            const nights = Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24));
            const subtotal = nights * pricePerNight;
            const serviceFee = Math.round(subtotal * 0.1); // 10% service fee
            const total = subtotal + serviceFee;
            
            document.getElementById('nightsCount').textContent = nights;
            document.getElementById('nightsText').textContent = nights;
            document.getElementById('subtotal').textContent = '$' + subtotal.toLocaleString();
            document.getElementById('serviceFee').textContent = '$' + serviceFee.toLocaleString();
            document.getElementById('totalAmount').textContent = '$' + total.toLocaleString();
            
            bookingSummary.style.display = 'block';
        } else {
            bookingSummary.style.display = 'none';
        }
    }
    
    // Date validation and calculation
    checkInInput.addEventListener('change', function() {
        const checkInDate = new Date(this.value);
        const nextDay = new Date(checkInDate);
        nextDay.setDate(nextDay.getDate() + 1);
        checkOutInput.min = nextDay.toISOString().split('T')[0];
        
        if (checkOutInput.value && new Date(checkOutInput.value) <= checkInDate) {
            checkOutInput.value = nextDay.toISOString().split('T')[0];
        }
        
        calculateBooking();
    });
    
    checkOutInput.addEventListener('change', calculateBooking);
});

function loadMoreReviews() {
    // Implementation for loading more reviews via AJAX
    console.log('Load more reviews functionality to be implemented');
}
</script>
@endsection
