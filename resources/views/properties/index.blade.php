@extends('layouts.frontend')

@section('title', 'Search Properties - Oasis Homestay')

@section('content')
<div class="container py-4">
    <!-- Search Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <form method="GET" action="{{ route('properties.search') }}" class="row g-3">
                        <div class="col-md-3">
                            <label for="location" class="form-label">Location</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="location" 
                                   name="location" 
                                   value="{{ request('location') }}"
                                   placeholder="Where are you going?">
                        </div>
                        <div class="col-md-2">
                            <label for="check_in" class="form-label">Check-in</label>
                            <input type="text"
                                   class="form-control date-picker"
                                   id="check_in"
                                   name="check_in"
                                   value="{{ request('check_in') }}"
                                   placeholder="Check-in date"
                                   readonly>
                        </div>
                        <div class="col-md-2">
                            <label for="check_out" class="form-label">Check-out</label>
                            <input type="text"
                                   class="form-control date-picker"
                                   id="check_out"
                                   name="check_out"
                                   value="{{ request('check_out') }}"
                                   placeholder="Check-out date"
                                   readonly>
                        </div>
                        <div class="col-md-2">
                            <label for="guests" class="form-label">Guests</label>
                            <select class="form-select" id="guests" name="guests">
                                <option value="">Any</option>
                                @for($i = 1; $i <= 10; $i++)
                                    <option value="{{ $i }}" {{ request('guests') == $i ? 'selected' : '' }}>
                                        {{ $i }} Guest{{ $i > 1 ? 's' : '' }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="sort" class="form-label">Sort by</label>
                            <select class="form-select" id="sort" name="sort">
                                <option value="featured" {{ request('sort') == 'featured' ? 'selected' : '' }}>Featured</option>
                                <option value="price_low" {{ request('sort') == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                                <option value="price_high" {{ request('sort') == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                                <option value="rating" {{ request('sort') == 'rating' ? 'selected' : '' }}>Highest Rated</option>
                                <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>Newest</option>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                    
                    <!-- Advanced Filters Toggle -->
                    <div class="mt-3">
                        <button class="btn btn-outline-secondary btn-sm" 
                                type="button" 
                                data-bs-toggle="collapse" 
                                data-bs-target="#advancedFilters">
                            <i class="fas fa-sliders-h me-1"></i>
                            Advanced Filters
                        </button>
                    </div>
                    
                    <!-- Advanced Filters -->
                    <div class="collapse mt-3" id="advancedFilters">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="min_price" class="form-label">Min Price</label>
                                <input type="number" 
                                       class="form-control" 
                                       id="min_price" 
                                       name="min_price"
                                       value="{{ request('min_price') }}"
                                       placeholder="$0">
                            </div>
                            <div class="col-md-3">
                                <label for="max_price" class="form-label">Max Price</label>
                                <input type="number" 
                                       class="form-control" 
                                       id="max_price" 
                                       name="max_price"
                                       value="{{ request('max_price') }}"
                                       placeholder="$1000">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="h4 mb-0">
                @if(request()->hasAny(['location', 'check_in', 'check_out', 'guests']))
                    Search Results
                    @if(request('location'))
                        in {{ request('location') }}
                    @endif
                @else
                    All Properties
                @endif
            </h2>
            <p class="text-muted mb-0">{{ $properties->total() }} properties found</p>
        </div>
        <div class="col-md-4 text-md-end">
            @if(request()->hasAny(['location', 'check_in', 'check_out', 'guests', 'min_price', 'max_price']))
                <a href="{{ route('properties.search') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-times me-1"></i>Clear Filters
                </a>
            @endif
        </div>
    </div>

    <!-- Properties Grid -->
    @if($properties->count() > 0)
        <div class="row">
            @foreach($properties as $property)
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card property-card h-100 shadow-sm border-0">
                    <div class="position-relative">
                        <img src="{{ $property->primary_image_url }}" 
                             class="card-img-top" 
                             alt="{{ $property->name }}"
                             style="height: 250px; object-fit: cover;">
                        
                        @if($property->featured)
                        <div class="position-absolute top-0 end-0 m-3">
                            <span class="badge bg-primary">Featured</span>
                        </div>
                        @endif
                        
                        <div class="position-absolute bottom-0 start-0 m-3">
                            <span class="badge bg-dark bg-opacity-75">
                                <i class="fas fa-star text-warning me-1"></i>
                                {{ number_format($property->average_rating, 1) }}
                                ({{ $property->reviews_count }})
                            </span>
                        </div>
                    </div>
                    
                    <div class="card-body d-flex flex-column">
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {{ $property->location->name }}
                            </small>
                        </div>
                        
                        <h5 class="card-title">{{ $property->name }}</h5>
                        
                        <p class="card-text text-muted flex-grow-1">
                            {{ Str::limit($property->description, 100) }}
                        </p>
                        
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="fas fa-users me-1"></i>{{ $property->guest_capacity }} guests
                                <i class="fas fa-bed ms-2 me-1"></i>{{ $property->bedrooms }} bedrooms
                                <i class="fas fa-bath ms-2 me-1"></i>{{ $property->bathrooms }} bathrooms
                            </small>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mt-auto">
                            <div>
                                <span class="h5 mb-0" style="color: @primaryColor();">
                                    ${{ number_format($property->price_per_night) }}
                                </span>
                                <small class="text-muted">/night</small>
                            </div>
                            <a href="{{ route('properties.show', $property->slug) }}" 
                               class="btn btn-outline-primary btn-sm">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="row">
            <div class="col-12">
                {{ $properties->appends(request()->query())->links() }}
            </div>
        </div>
    @else
        <!-- No Results -->
        <div class="row">
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h3>No properties found</h3>
                    <p class="text-muted mb-4">
                        Try adjusting your search criteria or browse all properties.
                    </p>
                    <a href="{{ route('properties.search') }}" class="btn btn-primary">
                        Browse All Properties
                    </a>
                </div>
            </div>
        </div>
    @endif
</div>

<style>
.property-card:hover {
    transform: translateY(-5px);
    transition: transform 0.3s ease;
}

.btn-primary {
    background-color: @secondaryColor();
    border-color: @secondaryColor();
}

.btn-primary:hover {
    background-color: #236B47;
    border-color: #236B47;
}

.btn-outline-primary {
    color: @secondaryColor();
    border-color: @secondaryColor();
}

.btn-outline-primary:hover {
    background-color: @secondaryColor();
    border-color: @secondaryColor();
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Date validation
    const checkInInput = document.getElementById('check_in');
    const checkOutInput = document.getElementById('check_out');
    
    if (checkInInput && checkOutInput) {
        checkInInput.addEventListener('change', function() {
            const checkInDate = new Date(this.value);
            const nextDay = new Date(checkInDate);
            nextDay.setDate(nextDay.getDate() + 1);
            checkOutInput.min = nextDay.toISOString().split('T')[0];
            
            if (checkOutInput.value && new Date(checkOutInput.value) <= checkInDate) {
                checkOutInput.value = nextDay.toISOString().split('T')[0];
            }
        });
    }
});
</script>
@endsection
